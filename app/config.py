import os
import sys
from typing import Any, Dict, List, Optional
from loguru import logger
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field

from commons.extensions.logger_extras import DEFAULT_LOG_FORMAT, es_api_sink, es_sink, log_server_name, log_uid


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file='.env', env_file_encoding='utf-8')

    ROOT_PATH: str = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))

    DEBUG: bool = Field(False, description="是否开启debug模式")
    #
    LOG_PATH: str = Field(os.path.join(ROOT_PATH, 'logs'), description="日志路径")
    LOG_LEVEL: str = Field('INFO', description="日志等级")
    LOG_FORMAT: str = Field(DEFAULT_LOG_FORMAT, description="日志格式")
    LOG_BACKTRACE: bool = Field(True, description="是否开启日志回溯")
    LOG_DIAGNOSE: bool = Field(True, description="是否开启日志诊断")
    LOG_ENQUEUE: bool = Field(True, description="保证进程、线程安全")
    ES_LOG_TAG: str = Field('elasticsearch', description="ES日志标签")
    ES_LOG_LEVEL: str = Field('INFO', description="ES日志等级")
    ES_LOG_ROTATION: str = Field('500 MB', description="ES日志文件大小")
    # ========= REDIS ==========
    REDIS_URL: str = Field(..., description="redis连接url")
    REDIS_MAX_CONNECTIONS: int = Field(50, description="redis最大连接数")
    REDIS_DECODE_RESPONSES: bool = Field(True, description="redis是否解码响应")
    REDIS_SOCKET_TIMEOUT: int = Field(10, description="redis连接超时时间，单位秒，仅同步方式生效")
    REDIS_HEALTH_CHECK_INTERVAL: int = Field(60, description="redis健康检查间隔，单位秒，仅异步方式生效")

    # ========= FastAPI ==========
    FASTAPI_INIT_OPTIONS: Optional[Dict] = Field(
        {'docs_url': '', 'redoc_url': '', 'openapi_url': '', 'swagger_ui_oauth2_redirect_url': ''},
        description="fastapi配置",
    )

    FASTAPI_CORS_ORIGINS: Optional[List[str]] = Field(
        ['http://localhost:9528', 'http://127.0.0.1:9528'], description="跨域白名单"
    )

    API_PERFIX: str = Field('/api/v1/crawler/vz', description="api前缀")
    # ========= 业务配置 ==========
    BOOK_QUEUE: str = Field('tr_book_queue', description="预订任务队列")
    PAY_QUEUE: str = Field('tr_pay_queue', description="支付任务队列")
    ORDER_CHECK_QUEUE: str = Field('tr_check_queue', description="检查任务队列")
    FLIGHT_FARE_URL: str = Field('http://127.0.0.1:8080', description="运价模块API地址")
    FLIGHT_ORDER_URL: str = Field('http://127.0.0.1:8081', description="订单模块API地址")
    FLIGHT_PRE_ORDER_URL: str = Field('http://*************:8082', description="压位模块API地址")
    LOGIN_URL: str = Field(f'/api/v1/flight_fare/admin/login/token', description="登录url,注意前缀配置成运价系统地址")
    PAY_CENTER_URL: str = Field('http://*************:9529', description="支付中心API地址")

   

    CELERY_BROKER_URL: str = Field(..., description="celery任务池地址")
    CELERY_RESULT_BACKEND: str = Field(..., description="celery结果存储地址")
    CELERY_TASK_SERIALIZER: str = Field('json', description="celery任务序列化类型")
    CELERY_RESULT_SERIALIZER: str = Field('json', description="celery结果序列化类型")
    CELERY_ACCEPT_CONTENT: List[str] = Field(['json'], description="celery任务接受内容")
    CELERY_BROKER_CONNECTION_RETRY: bool = Field(
        True, description="celery对链接失败进行重试"
    )  # celery_broker_connection_retry
    CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP: bool = Field(True, description="celery启动时进行链接重试")
    CELERY_TASK_ROUTES: Dict[str, str] = Field(
        {
            'vz_search_task': 'vz_search_queue',
            'vz_verify_task': 'vz_verify_queue',
            'vz_hood_task': 'vz_hood_queue',
            'vz_verify_book_task': 'vz_verify_book_queue',
            'vz_scan_book_task': 'vz_scan_book_queue',
            'vz_hood_book_task': 'vz_hood_book_queue',
        },
        description="celery task routes",
    )
    CELERY_WORKER_HIJACK_ROOT_LOGGER: bool = Field(False, description="是否启用 Celery 接管日志")
    THIRD_PROXY_MOD_NAME: str = Field('', description="第三方代理模块名称")
    PROXIES_BY_GROUPED: Dict[str, List[str]] = Field(
        {'default': [], 'hood': [], 'book': []}, description="按用途分组的代理列表"
    )
    PROXY_POOL_KEY: str = Field(None, description="代理池地址")
    WAIT_PROXY_INTERVAL: float = Field(0.0, description="等待代理间隔,0.0表示可以走本机")
    # 延迟释放系数
    PROXY_UNLOCK_DELAY_RANGE: List[int] = Field([0, 0], description="代理锁延迟系数")
    FORBIDDEN_DELAY: int = Field(600, description="触发封禁的代理延迟时间")
    SCAN_TIMES: int = Field(10, description="扫描次数")
    AES_KEY: str = Field(..., description="AES加密密钥")
    # 任务结束延迟系数
    TASK_FINISH_MIN_DELAY: int = Field(0, description="任务结束最小延迟")
    TASK_FINISH_MAX_DELAY: int = Field(0, description="任务结束最大延迟")
    INNER_IP_MAP: Dict[str, str] = Field(default_factory=dict, description="内网IP映射")

settings = Settings()

# ========== 日志配置 ==========
log_server_name.set('vz_crawler')
logger.remove()
logger.add(
    sys.stderr,
    level=settings.LOG_LEVEL,
    format=settings.LOG_FORMAT,
    backtrace=settings.LOG_BACKTRACE,
    diagnose=settings.LOG_DIAGNOSE,
    filter=lambda record: record["extra"].get("write_tag") is None,
)


logger.add(
    es_sink, level="INFO", filter=lambda record: record["extra"].get("write_tag") == 'elasticsearch', serialize=True
)

logger.add(
    es_api_sink, level="INFO", filter=lambda record: record["extra"].get("write_tag") == 'api_es_log', serialize=True
)
logger.opt(exception=True)
logger.configure(extra={"unique_id": log_uid})

logger.debug('config loaded')

SYNC_REDIS_CFG = {
    "redis_url": settings.REDIS_URL.split('?')[0],
    "decode_responses": settings.REDIS_DECODE_RESPONSES,
    "max_connections": settings.REDIS_MAX_CONNECTIONS,
    "socket_timeout": settings.REDIS_SOCKET_TIMEOUT,
}

ASYNC_REDIS_CFG = {
    "redis_url": settings.REDIS_URL.split('?')[0],
    "decode_responses": settings.REDIS_DECODE_RESPONSES,
    "max_connections": settings.REDIS_MAX_CONNECTIONS,
    "health_check_interval": settings.REDIS_HEALTH_CHECK_INTERVAL,
}
