import os
import sys
from typing import Any, Dict, List, Optional
from loguru import logger
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field

from commons.extensions.logger_extras import DEFAULT_LOG_FORMAT, es_api_sink, es_sink, log_server_name, log_uid


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file='.env', env_file_encoding='utf-8')
    SERVER_NAME: str = Field('proxy_server', description="服务器名称")
    ROOT_PATH: str = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))

    DEBUG: bool = Field(False, description="是否开启debug模式")
    #
    LOG_PATH: str = Field(os.path.join(ROOT_PATH, 'logs'), description="日志路径")
    LOG_LEVEL: str = Field('INFO', description="日志等级")
    LOG_FORMAT: str = Field(DEFAULT_LOG_FORMAT, description="日志格式")
    LOG_BACKTRACE: bool = Field(True, description="是否开启日志回溯")
    LOG_DIAGNOSE: bool = Field(True, description="是否开启日志诊断")
    LOG_ENQUEUE: bool = Field(True, description="保证进程、线程安全")

    # ========= MYSQL ==========
    # 异步操作数据库
    SQLALCHEMY_DATABASE_URI: str = Field(
        ..., description="异步数据库连接url"
    )
    SQLALCHEMY_ECHO: bool = Field(False, description="是否开启sqlalchemy echo")
    # 每n秒检查一次连接池（重要，可避免链接超时断开）
    SQLALCHEMY_POOL_RECYCLE: int = Field(7200, description="每n秒检查一次连接池（重要，可避免链接超时断开）")
    # 连接池最大连接数
    SQLALCHEMY_POOL_SIZE: int = Field(50, description="连接池最大连接数")
    # 连接池最大等待时间
    SQLALCHEMY_POOL_TIMEOUT: int = Field(30, description="连接池最大等待时间")
    # 连接池超出最大连接数时，最大超出上限
    SQLALCHEMY_MAX_OVERFLOW: int = Field(10, description="连接池超出最大连接数时，最大超出上限")
    
    # ========= REDIS ==========
    REDIS_URL: str = Field(..., description="redis连接url")
    REDIS_MAX_CONNECTIONS: int = Field(50, description="redis最大连接数")
    REDIS_DECODE_RESPONSES: bool = Field(True, description="redis是否解码响应")
    REDIS_SOCKET_TIMEOUT: int = Field(10, description="redis连接超时时间，单位秒，仅同步方式生效")
    REDIS_HEALTH_CHECK_INTERVAL: int = Field(60, description="redis健康检查间隔，单位秒，仅异步方式生效")

    # ========= FastAPI ==========
    FASTAPI_INIT_OPTIONS: Optional[Dict] = Field(
        {'docs_url': '', 'redoc_url': '', 'openapi_url': '', 'swagger_ui_oauth2_redirect_url': ''},
        description="fastapi配置",
    )

    FASTAPI_CORS_ORIGINS: Optional[List[str]] = Field(
        ['http://localhost:9528', 'http://127.0.0.1:9528'], description="跨域白名单"
    )

    API_PERFIX: str = Field('/api/v1/proxy_server', description="api前缀")
    # ========= 业务配置 ==========
    

    PROXY_POOL_SUFFIX: str = Field('proxy_pool', description="代理池后缀")


settings = Settings()

# ========== 日志配置 ==========
log_server_name.set(settings.SERVER_NAME)
logger.remove()
logger.add(
    sys.stderr,
    level=settings.LOG_LEVEL,
    format=settings.LOG_FORMAT,
    backtrace=settings.LOG_BACKTRACE,
    diagnose=settings.LOG_DIAGNOSE,
    filter=lambda record: record["extra"].get("write_tag") is None,
)


logger.add(
    es_sink, level="INFO", filter=lambda record: record["extra"].get("write_tag") == 'elasticsearch', serialize=True
)

logger.add(
    es_api_sink, level="INFO", filter=lambda record: record["extra"].get("write_tag") == 'api_es_log', serialize=True
)
logger.opt(exception=True)
logger.configure(extra={"unique_id": log_uid})

logger.debug('config loaded')

SYNC_REDIS_CFG = {
    "redis_url": settings.REDIS_URL.split('?')[0],
    "decode_responses": settings.REDIS_DECODE_RESPONSES,
    "max_connections": settings.REDIS_MAX_CONNECTIONS,
    "socket_timeout": settings.REDIS_SOCKET_TIMEOUT,
}

ASYNC_REDIS_CFG = {
    "redis_url": settings.REDIS_URL.split('?')[0],
    "decode_responses": settings.REDIS_DECODE_RESPONSES,
    "max_connections": settings.REDIS_MAX_CONNECTIONS,
    "health_check_interval": settings.REDIS_HEALTH_CHECK_INTERVAL,
}
