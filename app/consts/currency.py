from enum import unique
from commons.cores.base_const import BaseEnum


@unique
class CurrencyCodes(BaseEnum):
    """乘客类型"""

    HKD = ('HKD', '港币')
    CNY = ('CNY', '人民币')
    VND = ('VND', '越南盾')
    THB = ('THB', '泰铢')
    USD = ('USD', '美元')
    JPY = ('JPY', '日元')
    INR = ('INR', '印度卢比')
    TWD = ('TWD', '台币')
    MYR = ('MYR', '马来盾')
    SGD = ('SGD', '新加坡元')
    KRW = ('KRW', '韩元')
    AUD = ('AUD', '澳元')
    IDR = ('IDR', '印度尼西亚盾')
