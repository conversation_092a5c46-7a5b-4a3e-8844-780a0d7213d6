from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Index, UniqueConstraint
from sqlalchemy.sql import func

from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions
from commons.consts.common_status import EnableStatus

class HostOrm(BaseDBModel, MixinFields, MixinFunctions):
    """域名与有效host映射关系表"""
    __tablename__ = 'hosts'
    __table_args__ = (
        UniqueConstraint('domain', 'host', name='uix_domain_host'),
        {'comment': '域名与有效host映射关系表'})
    
    # 域名
    domain = Column(String(64), nullable=False, server_default='', comment='域名')
    # 有效的host
    host = Column(String(32), nullable=False, server_default='', comment='有效的host')
    # 航司代码
    airline_code = Column(String(8), nullable=False, server_default='', comment='航司二字码')
    # 是否启用
    status = Column(String(16), nullable=False, server_default=EnableStatus.ENABLED.value, comment='是否启用')