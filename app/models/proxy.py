from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions
from sqlalchemy import DATETIME, Column, String, Integer


class ProxyOrm(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "proxies"
    __table_args__ = {"comment": "代理表"}

    # 代理信息
    # 协议
    protocol = Column(String(8), nullable=False, server_default='http', comment="代理协议:http/https/socks5")
    ip = Column(String(32), nullable=False, server_default='', comment="代理IP")
    port = Column(String(8), nullable=False, server_default='', comment="代理端口")
    username = Column(String(64), nullable=False, server_default='', comment="代理账号")
    password = Column(String(64), nullable=False, server_default='', comment="代理密码")
    
    # 地理信息
    country_code = Column(String(8), nullable=False, server_default='', comment="国家二字码")
    region_code = Column(String(64), nullable=False, server_default='', comment="地区码")
    
    # 时间信息
    expire_time = Column(DATETIME, nullable=True, comment="失效时间")
    last_provided_time = Column(DATETIME, nullable=True, comment="最近对外提供时间")
    
    # 其他信息
    platform_site = Column(String(128), nullable=False, server_default='', comment="第三方平台标识") 