from datetime import datetime
import time
from celery import Celery
from loguru import logger
import orjson
from app.config import settings
from app.services import callback_service, pre_order_service, vz_agent_service, vz_hood_service, vz_search_service
from commons.consts.api_codes import ApiCodes

celery_app = Celery(broker=settings.CELERY_BROKER_URL, backend=settings.CELERY_RESULT_BACKEND)
celery_app.conf.update(
    task_serializer=settings.CELERY_TASK_SERIALIZER,
    accept_content=settings.CELERY_ACCEPT_CONTENT,  # Ignore other content
    result_serializer=settings.CELERY_RESULT_SERIALIZER,
    task_routes=settings.CELERY_TASK_ROUTES,
    broker_connection_retry=settings.CELERY_BROKER_CONNECTION_RETRY,
    broker_connection_retry_on_startup=settings.CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP,
    worker_hijack_root_logger=settings.CELERY_WORKER_HIJACK_ROOT_LOGGER,
    broker_transport_options={
        'max_retries': 30,  # 最大重试次数
        'interval_start': 0.5,  # 初始重试间隔时间（秒）
        'interval_step': 0.5,  # 重试间隔递增步长
        'interval_max': 5,  # 最大重试间隔时间
    },
    # worker日志格式直接输出，方便loguru处理
    worker_log_format='%(message)s',
    worker_task_log_format='%(message)s',
)


@celery_app.task(name='vz_search_task')
def search(task_data: dict):
    from commons.extensions.logger_extras import log_uid

    start_time = datetime.now()
    vz_search = vz_search_service.VZSearchService()
    vz_search.set_proxy(proxy_group='default', airline_code='VZ')
    wait_end = datetime.now()
    logger.info(f'等待代理时间: {(wait_end-start_time).total_seconds()} 秒')
    result = vz_search.run_search(task_data)
    end_time = datetime.now()
    vz_search.search_callback(result)

    try:
        if 'status' in task_data:
            del task_data['status']
        logger.bind(write_tag="elasticsearch").info(
            '',
            server_name="vz_search_worker",
            # unique_id=str(log_uid),
            wait_time=(wait_end - start_time).total_seconds(),
            cost_time=(end_time - wait_end).total_seconds(),
            status='success' if ApiCodes.SUCCESS.value == result['error']['code'] else 'failed',
            code=result['error']['code'],
            message=result['error']['message'],
            result=orjson.dumps(result, default=str).decode('utf-8'),
            start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
            end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
            host=vz_search.vz_client.host,
            proxy=list(vz_search.vz_client.session.proxies.values())[0] if vz_search.vz_client.session.proxies else '',
            **task_data,
        )
    except Exception as e:
        logger.warning(e)

    return result


@celery_app.task(name='vz_verify_task')
def verify(task_data: dict):
    from commons.extensions.logger_extras import log_uid

    start_time = datetime.now()
    vz_search = vz_search_service.VZSearchService()
    vz_search.set_proxy(proxy_group='default', airline_code='VZ')
    wait_end = datetime.now()
    logger.info(f'等待代理时间: {(wait_end-start_time).total_seconds()} 秒')
    result = vz_search.run_search(task_data)
    end_time = datetime.now()
    vz_search.verify_callback(result)

    try:
        if 'status' in task_data:
            del task_data['status']
        logger.bind(write_tag="elasticsearch").info(
            '',
            server_name="vz_verify_worker",
            # unique_id=str(log_uid),
            wait_time=(wait_end - start_time).total_seconds(),
            cost_time=(end_time - wait_end).total_seconds(),
            status='success' if ApiCodes.SUCCESS.value == result['error']['code'] else 'failed',
            code=result['error']['code'],
            message=result['error']['message'],
            result=orjson.dumps(result, default=str).decode('utf-8'),
            start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
            end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
            host=vz_search.vz_client.host,
            proxy=list(vz_search.vz_client.session.proxies.values())[0] if vz_search.vz_client.session.proxies else '',
            **task_data,
        )
    except Exception as e:
        logger.warning(e)

    return result


@celery_app.task(name='vz_verify_book_task')
def verify_book_task(params):
    from commons.extensions.logger_extras import log_uid

    start_time = datetime.now()
    result = vz_agent_service.run_verify_book(params)
    end_time = datetime.now()
    callback_service.verify_book_result_callback(result)

    try:

        if 'status' in params:
            del params['status']
        logger.bind(write_tag="elasticsearch").info(
            '',
            server_name="vz_verify_book_worker",
            # unique_id=str(log_uid),
            cost_time=(end_time - start_time).total_seconds(),
            status='success' if ApiCodes.SUCCESS.value == result['error']['code'] else 'failed',
            code=result['error']['code'],
            message=result['error']['message'],
            result=orjson.dumps(result, default=str).decode('utf-8'),
            start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
            end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
            **result['task_info'],
        )
    except Exception as e:
        logger.warning(e)
    return result


@celery_app.task(name='vz_scan_book_task')
def scan_book_task(params):
    from commons.extensions.logger_extras import log_uid

    start_time = datetime.now()
    result = vz_agent_service.run_scan_book(params)
    end_time = datetime.now()
    callback_service.scan_book_result_callback(result)

    try:

        if 'status' in params:
            del params['status']
        logger.bind(write_tag="elasticsearch").info(
            '',
            server_name="vz_scan_book_worker",
            # unique_id=str(log_uid),
            cost_time=(end_time - start_time).total_seconds(),
            status='success' if ApiCodes.SUCCESS.value == result['error']['code'] else 'failed',
            code=result['error']['code'],
            message=result['error']['message'],
            result=orjson.dumps(result, default=str).decode('utf-8'),
            start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
            end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
            **result['task_info'],
        )
    except Exception as e:
        logger.warning(e)
    return result


@celery_app.task(name='vz_hood_task')
def hood_task(params):
    """压位占座"""
    for _ in range(30):
        start_time = datetime.now()
        vz_hood = vz_hood_service.VZHoodService()
        # 回调在run_hood内部实现，每次占座成功就回调一次
        result = vz_hood.run_hood(params)
        end_time = datetime.now()

        pre_order_service.hood_callback(result)

        try:
            if 'status' in params:
                del params['status']
            logger.bind(write_tag="elasticsearch").info(
                '',
                server_name="vz_hood_worker",
                # unique_id=str(log_uid),
                cost_time=(end_time - start_time).total_seconds(),
                status='success' if ApiCodes.SUCCESS.value == result['error']['code'] else 'failed',
                code=result['error']['code'],
                message=result['error']['message'],
                result=orjson.dumps(result, default=str).decode('utf-8'),
                start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                host=vz_hood.vz_client.host,
                proxy=list(vz_hood.vz_client.session.proxies.values())[0] if vz_hood.vz_client.session.proxies else '',
                **result['task_info'],
            )
        except Exception as e:
            logger.warning(e)
        finally:
            if result['error']['code'] == ApiCodes.SUCCESS.value:
                time.sleep(1)
            else:
                # 无可压位的价格，退出压位
                break
    return True


@celery_app.task(name='vz_hood_book_task')
def hood_book_task(params):
    """压位出票"""
    from commons.extensions.logger_extras import log_uid

    start_time = datetime.now()
    result = vz_agent_service.run_hood_book(params)
    end_time = datetime.now()
    callback_service.hood_book_result_callback(result)

    try:

        if 'status' in params:
            del params['status']
        logger.bind(write_tag="elasticsearch").info(
            '',
            server_name="vz_hood_book_worker",
            # unique_id=str(log_uid),
            cost_time=(end_time - start_time).total_seconds(),
            status='success' if ApiCodes.SUCCESS.value == result['error']['code'] else 'failed',
            code=result['error']['code'],
            message=result['error']['message'],
            result=orjson.dumps(result, default=str).decode('utf-8'),
            start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
            end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
            **result['task_info'],
        )
    except Exception as e:
        logger.warning(e)
    return result
