import copy
from datetime import datetime, timedelta
import random
import time
from typing import Union
import curl_cffi
from curl_cffi import requests
from loguru import logger
import orjson
import urllib.parse

import pytz

# 屏蔽不兼容的浏览器和移动设备标记
BROWSER_TYPES = [
    # Edge
    "edge99",
    "edge101",
    # Chrome
    "chrome99",
    "chrome100",
    "chrome101",
    "chrome104",
    "chrome107",
    "chrome110",
    "chrome116",
    "chrome119",
    "chrome120",
    "chrome123",
    # "chrome124",
    # "chrome99_android",
    # Safari
    "safari15_3",
    "safari15_5",
    "safari17_0",
    # "safari17_2_ios",
    # alias
    # "chrome",
    # "edge",
    # "safari",
    # "safari_ios",
    # "chrome_android",
]

PRINTABLE_TYPES = ['application/json', 'text/html']


class VZClient:
    def __init__(self):
        self.session = requests.Session()

        self.session.headers.update(
            {
                'host': 'th.vietjetair.com',
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.13; rv:109.0) Gecko/20100101 Firefox/115.0',
                'Accept': '*/*',
                'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
                'Accept-Encoding': 'gzip, deflate, br',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'X-CSRF-TOKEN': 'tBzmlkL7bOvP9nzd5N6KaAZBZu81isRG2zgCwRwb',
                'X-Requested-With': 'XMLHttpRequest',
                'Origin': 'https://th.vietjetair.com',
                'Connection': 'keep-alive',
                # 'Referer': 'https://th.vietjetair.com/booking/I9Q1Z85EF/passenger',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'Pragma': 'no-cache',
                'Cache-Control': 'no-cache',
            }
        )

        self.session.timeout = 30
        # self.session.verify = False
        self.impersonate = random.choice(BROWSER_TYPES)
        self.enable_delay_request = False

    @property
    def domain(self):
        # return 'https://*************'
        # return 'https://*************'
        return 'https://th.vietjetair.com'

    def _request(self, method, url, **kwargs) -> Union[None, dict, str, bytes]:
        result = None
        response_content_type = ''
        response = None
        try:
            if self.enable_delay_request:
                time.sleep(random.random())
            response = self.session.request(method, url, **kwargs)

            response_content_type = response.headers.get('content-type', '')
            if 'application/json' in response_content_type:
                result = response.json()
            elif 'text/html' in response_content_type:
                result = response.text
            else:
                result = response.content
            # self.session.cookies.update(response.cookies.get_dict())
        except Exception as e:
            logger.exception(f'{e} {self.session.proxies}')
        finally:
            if [c_type for c_type in PRINTABLE_TYPES if c_type in response_content_type]:
                debug_info = {
                    'url': url,
                    'headers': response.request.headers,
                    'response_cookies': response.cookies.get_dict(),
                    'cookies': self.session.cookies.get_dict(),
                    'kwargs': kwargs,
                    'response': result,
                }

                logger.debug(f'{debug_info}')
        return result

    def index_page(self):
        """首页 用于获取csrftoken"""
        url = f'{self.domain}/'
        resp_html = self._request('GET', url)
        return resp_html

    def search(
        self,
        from_where: str,
        to_where: str,
        start: str,
        end: str,
        trip_type: str = 'onewaytrip',
        adult_count: int = 1,
        child_count: int = 0,
        infant_count: int = 0,
        promo_code: str = '',
        currency: str = 'thb',
    ):
        """查询航班"""

        url = f'{self.domain}/flight/getFlights'
        params = {
            "tripType": trip_type,
            "from_where": from_where,
            "to_where": to_where,
            "start": start,
            "end": end,
            "adultCount": adult_count,
            "childCount": child_count,
            "infantCount": infant_count,
            "promoCode": promo_code,
            "currency": currency,
        }
        logger.debug(f'params: {params}')

        resp_json = self._request('GET', url, params=params)

        return resp_json

    def add_to_cart(self, api_uuid: str, trip_type: str, expired_time: int, departure: dict, seat_code: str):
        """添加航班到购物车"""

        fare = departure['fare_types'][seat_code]
        flight = departure['flights'][0]
        airline_code = flight['airline_code']
        flight_number = flight['flight_number']
        flight_no = airline_code + flight_number
        dep_date_short = departure['departureDate'].replace('-', '')
        # 'departure[flight_seat][uuid]': 'departure-fare-info-VZ20020250105VZ20020250105-Eco',
        seat_uuid = f'departure-fare-info-{flight_no}{dep_date_short}{flight_no}{dep_date_short}-{seat_code}'

        # # 获取航班起飞地的时区
        # target_timezone = pytz.timezone(flight['departure_date']['timezone'])

        # # Get the current time in the target timezone
        # tag_zone_curr_time = datetime.now(target_timezone)
        # system_time = datetime.now()  # 当前系统时区时间
        # zone_diff = (system_time - tag_zone_curr_time).total_seconds() / 3600
        # # Add 30 minutes to the current time
        # expired_time_str = (tag_zone_curr_time + timedelta(minutes=expired_booking)).strftime('%Y-%m-%d %H:%M:%S')

        data = {
            'api_uuid': api_uuid,
            'service_type': 'flight',
            'duration': departure['duration'],
            'departure_date': flight['departure_format_html'],
            'return_date': '',
            'tripType': trip_type,
            'adultCount': fare['availability'],
            'childCount': '0',
            'infantCount': '0',
            'departure[service_id]': departure['id'],
            'departure[departure_date]': flight['departure_date_html'],
            'departure[return_date]': flight['arrival_date_html'],
            'departure[duration]': departure['duration'],
            'departure[flight_seat][bookingKey]': fare['bookingKey'],
            'departure[flight_seat][totalFare]': fare['totalFare'],
            'departure[flight_seat][totalDiscount]': fare['totalDiscount'],
            'departure[flight_seat][allFareWithoutBag]': fare['allFareWithoutBag'],
            'departure[flight_seat][totalAdultFare]': fare['totalAdultFare'],
            'departure[flight_seat][totalChildFare]': fare['totalChildFare'],
            'departure[flight_seat][totalInfantFare]': fare['totalInfantFare'],
            'departure[flight_seat][adultCount]': fare['availability'],
            'departure[flight_seat][childCount]': '0',
            'departure[flight_seat][infantCount]': '0',
            'departure[flight_seat][priceIncludesTax]': fare['priceIncludesTax'],
            'departure[flight_seat][basePrice]': fare['basePrice'],
            'departure[flight_seat][adultFare]': fare['totalAdultFare'],
            'departure[flight_seat][adult_price]': fare['totalAdultFare'],
            'departure[flight_seat][currency_code]': fare['currency_code'],
            'departure[flight_seat][childFare]': fare['totalChildFare'],
            'departure[flight_seat][infantFare]': fare['totalInfantFare'],
            'departure[flight_seat][tax]': fare['tax'],
            'departure[flight_seat][name]': seat_code,
            'departure[flight_seat][code]': seat_code,
            'departure[flight_seat][max_passengers]': fare['max_passengers'],
            'departure[flight_seat][price_detail][fare_tax]': fare['price_detail']['fare_tax'],
            'departure[flight_seat][price_detail][tax_fee_tax]': fare['price_detail']['tax_fee_tax'],
            'departure[flight_seat][price_detail][addon_tax]': fare['price_detail']['addon_tax'],
            'departure[flight_seat][price_detail][fare_base]': fare['price_detail']['fare_base'],
            'departure[flight_seat][price_detail][tax_fee_base]': fare['price_detail']['tax_fee_base'],
            'departure[flight_seat][price_detail][addon_base]': fare['price_detail']['addon_base'],
            'departure[flight_seat][price_detail][fare_discount]': fare['price_detail']['fare_discount'],
            'departure[flight_seat][price_detail][tax_fee_discount]': fare['price_detail']['tax_fee_discount'],
            'departure[flight_seat][price_detail][addon_discount]': fare['price_detail']['addon_discount'],
            'departure[flight_seat][price_detail][fare_total]': fare['price_detail']['fare_total'],
            'departure[flight_seat][price_detail][tax_fee_total]': fare['price_detail']['tax_fee_total'],
            'departure[flight_seat][price_detail][addon_total]': fare['price_detail']['addon_total'],
            # 'departure[flight_seat][price_detail][tax_fee][AI][amount]': fare['price_detail']['tax_fee']['AI'][
            #     'amount'
            # ],
            # 'departure[flight_seat][price_detail][tax_fee][AI][baseAmount]': fare['price_detail']['tax_fee']['AI'][
            #     'baseAmount'
            # ],
            # 'departure[flight_seat][price_detail][tax_fee][AI][taxAmount]': fare['price_detail']['tax_fee']['AI'][
            #     'taxAmount'
            # ],
            # 'departure[flight_seat][price_detail][tax_fee][AI][discountAmount]': fare['price_detail']['tax_fee']['AI'][
            #     'discountAmount'
            # ],
            # 'departure[flight_seat][price_detail][tax_fee][AI][count]': fare['price_detail']['tax_fee']['AI']['count'],
            # 'departure[flight_seat][price_detail][tax_fee][AI][name]': fare['price_detail']['tax_fee']['AI']['name'],
            # 'departure[flight_seat][price_detail][tax_fee][AI][currency]': fare['price_detail']['tax_fee']['AI'][
            #     'currency'
            # ],
            # 'departure[flight_seat][price_detail][tax_fee][AM][amount]': fare['price_detail']['tax_fee']['AM'][
            #     'amount'
            # ],
            # 'departure[flight_seat][price_detail][tax_fee][AM][baseAmount]': fare['price_detail']['tax_fee']['AM'][
            #     'baseAmount'
            # ],
            # 'departure[flight_seat][price_detail][tax_fee][AM][taxAmount]': fare['price_detail']['tax_fee']['AM'][
            #     'taxAmount'
            # ],
            # 'departure[flight_seat][price_detail][tax_fee][AM][discountAmount]': fare['price_detail']['tax_fee']['AM'][
            #     'discountAmount'
            # ],
            # 'departure[flight_seat][price_detail][tax_fee][AM][count]': fare['price_detail']['tax_fee']['AM']['count'],
            # 'departure[flight_seat][price_detail][tax_fee][AM][name]': fare['price_detail']['tax_fee']['AM']['name'],
            # 'departure[flight_seat][price_detail][tax_fee][AM][currency]': fare['price_detail']['tax_fee']['AM'][
            #     'currency'
            # ],
            # 'departure[flight_seat][price_detail][tax_fee][HA][amount]': fare['price_detail']['tax_fee']['HA'][
            #     'amount'
            # ],
            # 'departure[flight_seat][price_detail][tax_fee][HA][baseAmount]': fare['price_detail']['tax_fee']['HA'][
            #     'baseAmount'
            # ],
            # 'departure[flight_seat][price_detail][tax_fee][HA][taxAmount]': fare['price_detail']['tax_fee']['HA'][
            #     'taxAmount'
            # ],
            # 'departure[flight_seat][price_detail][tax_fee][HA][discountAmount]': fare['price_detail']['tax_fee']['HA'][
            #     'discountAmount'
            # ],
            # 'departure[flight_seat][price_detail][tax_fee][HA][count]': fare['price_detail']['tax_fee']['HA']['count'],
            # 'departure[flight_seat][price_detail][tax_fee][HA][name]': fare['price_detail']['tax_fee']['HA']['name'],
            # 'departure[flight_seat][price_detail][tax_fee][HA][currency]': fare['price_detail']['tax_fee']['HA'][
            #     'currency'
            # ],
            'departure[flight_seat][price_detail][fare][adult][amount]': fare['price_detail']['fare']['adult'][
                'amount'
            ],
            'departure[flight_seat][price_detail][fare][adult][baseAmount]': fare['price_detail']['fare']['adult'][
                'baseAmount'
            ],
            'departure[flight_seat][price_detail][fare][adult][taxAmount]': fare['price_detail']['fare']['adult'][
                'taxAmount'
            ],
            'departure[flight_seat][price_detail][fare][adult][discountAmount]': fare['price_detail']['fare']['adult'][
                'discountAmount'
            ],
            'departure[flight_seat][price_detail][fare][adult][count]': fare['price_detail']['fare']['adult']['count'],
            'departure[flight_seat][price_detail][fare][adult][name]': fare['price_detail']['fare']['adult']['name'],
            'departure[flight_seat][price_detail][fare][adult][currency]': fare['price_detail']['fare']['adult'][
                'currency'
            ],
            'departure[flight_seat][price_display_select_fare]': fare['price_display_select_fare'],
            'departure[flight_seat][totalBeforeDiscount]': fare['totalBeforeDiscount'],
            'departure[flight_seat][availability]': fare['availability'],
            # 'departure[flight_seat][uuid]': 'departure-fare-info-VZ20020250105VZ20020250105-Eco',
            'departure[flight_seat][uuid]': seat_uuid,
            'departure[detail]': orjson.dumps(departure).decode('utf-8'),
            'departure[channel_provider]': departure['channel_provider'],
            'return': '',
            'expiredTime': expired_time,
            'term_conditions': 'on',
            'currency': fare['currency_code'].lower(),
            'promoCode': '',
            'promoCodeDiscount': '',
        }
        # tax_fee是动态的
        for fk, fv in fare['price_detail']['tax_fee'].items():
            data[f'departure[flight_seat][price_detail][tax_fee][{fk}][amount]'] = fv['amount']
            data[f'departure[flight_seat][price_detail][tax_fee][{fk}][baseAmount]'] = fv['baseAmount']
            data[f'departure[flight_seat][price_detail][tax_fee][{fk}][taxAmount]'] = fv['taxAmount']
            data[f'departure[flight_seat][price_detail][tax_fee][{fk}][discountAmount]'] = fv['discountAmount']
            data[f'departure[flight_seat][price_detail][tax_fee][{fk}][count]'] = fv['count']
            data[f'departure[flight_seat][price_detail][tax_fee][{fk}][name]'] = fv['name']
            data[f'departure[flight_seat][price_detail][tax_fee][{fk}][currency]'] = fv['currency']
            # 'departure[flight_seat][price_detail][tax_fee][AI][amount]': fare['price_detail']['tax_fee']['AI'][
            #     'amount'
            # ],
            # 'departure[flight_seat][price_detail][tax_fee][AI][baseAmount]': fare['price_detail']['tax_fee']['AI'][
            #     'baseAmount'
            # ],
            # 'departure[flight_seat][price_detail][tax_fee][AI][taxAmount]': fare['price_detail']['tax_fee']['AI'][
            #     'taxAmount'
            # ],
            # 'departure[flight_seat][price_detail][tax_fee][AI][discountAmount]': fare['price_detail']['tax_fee']['AI'][
            #     'discountAmount'
            # ],
            # 'departure[flight_seat][price_detail][tax_fee][AI][count]': fare['price_detail']['tax_fee']['AI']['count'],
            # 'departure[flight_seat][price_detail][tax_fee][AI][name]': fare['price_detail']['tax_fee']['AI']['name'],
            # 'departure[flight_seat][price_detail][tax_fee][AI][currency]': fare['price_detail']['tax_fee']['AI'][
            #     'currency'
            # ],
        url = f'{self.domain}/booking/addToCart'
        resp_json = self._request('POST', url, data=data)
        return resp_json

    def get_countries(self):
        """获取国家列表"""

        url = f'{self.domain}/booking/getCountries'
        resp_json = self._request('GET', url)
        return resp_json

    def get_ssr(self, booking_code):
        """获取SSR（辅营项目）"""

        url = f'{self.domain}/booking/getSSR'
        params = {'code': booking_code}
        resp_json = self._request('GET', url, params=params)
        return resp_json

    def loyalty(self, email):
        """获取会员信息"""

        url = f'{self.domain}/booking/loyalty'
        params = {'email': email}
        resp_json = self._request('GET', url, params=params)
        return resp_json

    def quotation(self, booking_form: dict):
        """填写乘机人并获取报价"""

        url = f'{self.domain}/booking/quotation'
        # headers = copy.deepcopy(self.session.headers)
        # headers['referer'] = 'https://th.vietjetair.com/booking/' + booking_form[0][1] + '/passenger'
        # resp_json = self._request('POST', url, data=booking_form, headers=headers)
        resp_json = self._request('POST', url, data=booking_form)
        return resp_json

    def booking_checkout(self, booking_code):
        """确认订单"""

        url = f'{self.domain}/booking/{booking_code}/checkout'
        resp_json = self._request('GET', url)
        return resp_json

    def check_booking_status(self, booking_code):
        """检查预定状态"""

        timetemp = int(time.time() * 1000)
        url = f'{self.domain}/booking/{booking_code}/check-status'
        params = {'_': timetemp}
        resp_json = self._request('GET', url, params=params)
        return resp_json

    def booking_do_checkout(self, booking_code):
        """提交确认"""
        url = f'{self.domain}/booking/doCheckout'
        data = {'code': booking_code}
        resp_json = self._request('POST', url, data=data)
        return resp_json

    def payment(self, booking_code):
        url = f'{self.domain}/booking/{booking_code}/payment'
        resp_json = self._request('GET', url, timeout=120)
        return resp_json

    def check_payment_fee(
        self,
        booking_code: str,
        group: str,
        fee: str,
        key: str,
        identifier: str,
        code: str,
        gateway: str,
        # 注意 py 3.10以下不支持 float | int 这种类型定义方式
        funcoin_limit: Union[float, int],
    ):
        """检查支付费用"""
        url = f'{self.domain}/booking/check-payment-fee'
        data = {
            'code': booking_code,
            'payment_selected[group]': group,
            'payment_selected[fee]': fee,
            'payment_selected[key]': key,
            'payment_selected[identifier]': identifier,
            'payment_selected[gateway]': gateway,
            'payment_selected[code]': code,
            'funcoin_limit': funcoin_limit,
        }

        resp_json = self._request('POST', url, data=data)
        return resp_json

    def do_payment(
        self,
        booking_code: str,
        group: str,
        minimum: str,
        exchange_rate: str,
        apply_voucher: str = 'false',
        using_money: str = '0',
        using_coin: str = '0',
        maximum: str = '0',
        credit: str = '0',
        auto: str = '1',
    ):
        """确认支付方式（锁单）"""
        url = f'{self.domain}/booking/doPayment'
        data = {
            'code': booking_code,
            'payment-group': group,
            'applyVoucher': apply_voucher,
            'funcoin_selected[using_money]': using_money,
            'funcoin_selected[using_coin]': using_coin,
            'funcoin_selected[maximum]': maximum,
            'funcoin_selected[minimum]': minimum,
            'funcoin_selected[exchange_rate]': exchange_rate,
            'credit': credit,
            'auto': auto,
        }

        resp_json = self._request('POST', url, data=data)
        return resp_json
