from fastapi import APIRouter, HTTPException, Response
import orjson
from pydantic import BaseModel

from app.config import settings
from app.services import tls_service
from commons import sdks as hy_sdks

routers = APIRouter(prefix=f'{settings.API_PERFIX}/tls', tags=['TLS辅助接口'])


# 普通接口
@routers.post("/forward", summary="转发请求", description="转发请求")
async def forward(item: hy_sdks.helper_service.tls.HelperServiceTlsForwardRequest):
    params = item.model_dump(exclude_none=True)
    tls_type = params.pop('tls_type', 'curl_cffi')

    if tls_type == 'curl_cffi':
        result = await tls_service.forward_by_curl_cffi(**params)
    else:
        result = {'response': None, 'status_code': 0, 'headers': None, 'cookies': None, 'exception': 'TLS类型错误'}
    return Response(content=orjson.dumps(result, default=str).decode('utf-8'), media_type="application/json")
