from math import ceil
import random
from loguru import logger
from fastapi import APIRouter, Depends
import orj<PERSON>


from app import vz_tasks
from app.services import vz_search_service
from app.services import vz_agent_service
from app.services.vz_agent_service import VZAgentService
from commons.consts.api_codes import ApiCodes

from commons.depends import get_real_client_ip
from app.config import settings
from app.views.schemas import public_schemas
from commons.extensions.redis_extras import RedisPool
from commons.fastapi.schemas import common_schemas
from commons.sdks import crawler


routers = APIRouter(prefix=f'{settings.API_PERFIX}/public', tags=['对外开放的API接口'])


@routers.post("/search", summary="报价查询", response_model=public_schemas.SearchOut, response_model_exclude_none=True)
async def search(item: public_schemas.SearchIn, client_ip: str = Depends(get_real_client_ip)):
    conditions = item.model_dump(exclude_none=True, exclude_unset=True)
    # celery会立即回调，这里无法直接复用
    vz_search = vz_search_service.VZSearchService()
    if conditions.get('currency_code'):
        vz_search.currency = conditions.get('currency_code')

    result = vz_search.run_search(task_data=conditions)
    if result.get('error').get('code') != ApiCodes.SUCCESS.value:
        return ApiCodes(result.get('error').get('code')).generate_api_result(data=result.get('data'))
    return ApiCodes.SUCCESS.generate_api_result(data=result.get('data'))


@routers.post(
    "/celery/routes",
    summary="获取celery任务路由",
    response_model=common_schemas.BaseApiOut,
    response_model_exclude_none=True,
)
async def celery_routes(client_ip: str = Depends(get_real_client_ip)):
    logger.debug(vz_tasks.celery_app.conf.task_routes)
    return ApiCodes.SUCCESS.generate_api_result(data=vz_tasks.celery_app.conf.task_routes)


@routers.post(
    "/hood/create",
    summary="压位订座任务创建",
    response_model=common_schemas.BaseApiOut,
    response_model_exclude_none=True,
)
async def hood_create(item: crawler.create_hood.CreateHoodRequest, client_ip: str = Depends(get_real_client_ip)):
    task_params = item.model_dump(exclude_none=True, exclude_unset=True)
    vz_tasks.hood_task.apply_async((task_params,))
    return ApiCodes.SUCCESS.generate_api_result(data=True)


# @routers.post(
#     "/order/create", summary="订单预订", response_model=common_schemas.BaseApiOut, response_model_exclude_none=True
# )
# async def order_create(item: crawler.create_order.CreateOrderRequest, client_ip: str = Depends(get_real_client_ip)):
#     result = vz_agent_service.run_verify_book(item.model_dump(exclude_none=True, exclude_unset=True))

#     return ApiCodes.SUCCESS.generate_api_result(data=result['data'])
