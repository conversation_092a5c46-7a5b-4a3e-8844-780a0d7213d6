import asyncio
import copy
from datetime import datetime, timedelta
from math import ceil

import random
import re
import time

import curl_cffi
from loguru import logger
import orjson


from app.clients.vz_agent_client import VZAgentClient


from app.config import settings
from app.extensions.redis_locker import SYNC_REDIS_CFG
from app.services import proxy_service, vz_agent_helper, vz_search_service

import re
from bs4 import BeautifulSoup

from commons.consts.api_codes import ApiCodes
from commons.extensions.redis_extras import SyncRedisPool
from commons import sdks as hy_sdks
from commons.utils import CipherUtils


class VZAgentService:
    def __init__(self, username, password, airline_code: str = 'VZ') -> None:
        self.client = VZAgentClient()
        self.username = username
        self.password = password
        self._view_state = None
        self._view_state_generator = None
        self.airline_code = airline_code

    def set_proxy(self, proxy_group: str = 'default', airline_code: str = 'VZ'):
        proxies, proxy_auth, proxy_str, host = proxy_service.get_proxy(
            proxy_group=proxy_group, airline_code=airline_code
        )
        if proxies:
            self.client.session.proxies = proxies
        if proxy_auth:
            self.client.session.proxy_auth = proxy_auth

    def login(self):
        result = None
        try:
            # self.client.index()
            login_1 = self.client.login()
            login_2 = self.client.login(username=self.username, password=self.password)

            if '新密码:' in login_2:
                # 跳过密码修改
                login_2 = self.client.agent_pwd()
            # login_data = vz_agent_helper.get_form_data(login_1)
            # logger.debug(login_data)

            # login_data['txtAgentID'] = self.username
            # login_data['txtAgentPswd'] = self.password
            # login_2 = self.vz_agent_client.login(login_data)

            result = vz_agent_helper.get_form_data(login_2)

            # self._view_state = result.get('__VIEWSTATE')
            # self._view_state_generator = result.get('__VIEWSTATEGENERATOR')
            if '预订页面 - 代理方案' not in login_2:
                raise Exception('登录失败')

            # logger.debug(result)
            # if not result or not result.get('accessToken'):
            #     logger.warning(f'登录失败 {result}')
            #     raise Exception('登录失败')
        except Exception as e:
            logger.exception(e)
            raise e

        finally:
            # task_end_time = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")
            pass

        return result

    def save_cookies(self, key: str = 'vz_agent_cookies'):
        with SyncRedisPool(**SYNC_REDIS_CFG) as redis:
            cache_data = {
                '__VIEWSTATE': self.client._view_state,
                '__VIEWSTATEGENERATOR': self.client._view_state_generator,
                'lstCompanyList': self.client._lst_company_list,
                'cookies': self.client.session.cookies.get_dict(),
            }
            redis.set(key, orjson.dumps(cache_data).decode('utf-8'), ex=60 * 10)
        return True

    def load_cookies(self, key: str = 'vz_agent_cookies'):
        cookies = {}
        with SyncRedisPool(**SYNC_REDIS_CFG) as redis:
            cache_str = redis.get(key)
            if cache_str:
                cache_data = orjson.loads(cache_str)
                cookies = cache_data.get('cookies', {})
                self.client._view_state = cache_data.get('__VIEWSTATE')
                self.client._view_state_generator = cache_data.get('__VIEWSTATEGENERATOR')
                self.client._lst_company_list = cache_data.get('lstCompanyList')
        self.client.session.cookies.update(cookies)
        return cookies

    def search(
        self, dep: str, arr: str, date: str, adult: int = 1, child: int = 0, infant: int = 0, currency_code: str = 'THB'
    ):
        result = None
        try:
            # 1. 跳转查询页面
            if not self.client._view_state:
                self.client.agent_options()
            agent_opt_2 = self.client.agent_options(currency_code=currency_code)
            # next_data = get_form_data(agent_options_2)
            # logger.debug(next_data)

            # 2. 查询
            dep_date = datetime.strptime(date, '%Y-%m-%d')

            search_2 = self.client.search(
                dep=dep,
                arr=arr,
                dep_date=dep_date,
                adult=adult,
                child=child,
                infant=infant,
                currency_code=currency_code,
                timeout=10,
                max_retries=3,
                pre_call=self.set_proxy,
                exceptions=(
                    curl_cffi.requests.exceptions.Timeout,
                    curl_cffi.requests.exceptions.HTTPError,
                    curl_cffi.requests.exceptions.ConnectionError,
                ),
            )
            logger.debug(search_2)

            result = vz_agent_helper.parse_flight(
                html=search_2,
                date=dep_date.strftime('%d/%m/%Y'),
                passenger_num=adult + child + infant,
                airline_code=self.airline_code,
            )
            # result = vz_agent_helper.get_form_data(search_2)
            result['extra'] = {
                'cookies': self.client.session.cookies.get_dict(),
                'view_state': self.client._view_state,
                'view_state_generator': self.client._view_state_generator,
                'lst_company_list': self.client._lst_company_list,
            }

        except Exception as e:
            logger.exception(e)
        finally:
            pass
        return result

    def book(self, fare_key: str, passengers: list):
        result = None

        # 选择航班和报价（加购物车）
        travel_opt_2 = self.client.travel_options(
            fare_key=fare_key,
            max_retries=3,
            pre_call=self.set_proxy,
            exceptions=(
                curl_cffi.requests.exceptions.Timeout,
                curl_cffi.requests.exceptions.HTTPError,
                curl_cffi.requests.exceptions.ConnectionError,
            ),
        )
        # logger.debug(travel_opt_2)
        # time.sleep(30)

        # 循环判断，如果儿童在第一个，则移到末尾
        for i, passenger in enumerate(passengers):
            if passenger['passenger_type'] == 'child':
                passengers.append(passengers.pop(i))
                break
        # 如果全部乘客都是儿童则抛出异常

        if len([p for p in passengers if p['passenger_type'] == 'child']) == len(passengers):
            raise Exception('全部乘客都是儿童，无法预订')
        # 填写乘机人
        details_2 = self.client.details(
            passengers=passengers,
            max_retries=3,
            pre_call=self.set_proxy,
            exceptions=(
                # curl_cffi.requests.exceptions.Timeout,
                curl_cffi.requests.exceptions.HTTPError,
                curl_cffi.requests.exceptions.ConnectionError,
            ),
        )
        if '预订页面 - 座位安置' not in details_2:
            err_ext = vz_agent_helper.get_error_msg(details_2)
            logger.warning(f'{details_2} 乘客提交失败 details {err_ext}')
            raise Exception(f'乘客提交失败 details {err_ext}')
        else:
            logger.info(f'乘客提交成功 details_2')
        # with open('tests/details_2.html', 'w', encoding='utf-8') as f:
        #     f.write(details_2)

        addons = vz_agent_helper.get_form_data(details_2, ignore_value_check=True)
        logger.debug(addons)

        passengers = vz_agent_helper.update_baggage_options(
            airline_code=self.airline_code, html_content=details_2, passengers=passengers
        )

        addons_form_data = vz_agent_helper.get_addons_form_data(addons=addons, passengers=passengers)

        addons_2 = self.client.addons(
            form_data=addons_form_data,
            max_retries=3,
            pre_call=self.set_proxy,
            exceptions=(
                # curl_cffi.requests.exceptions.Timeout,
                curl_cffi.requests.exceptions.HTTPError,
                curl_cffi.requests.exceptions.ConnectionError,
            ),
        )
        if '预订页面 - 付款资讯' not in addons_2:
            err_ext = vz_agent_helper.get_error_msg(addons_2)
            logger.warning(f'{addons_2} 辅营提交失败 addons {err_ext}')
            raise Exception(f'辅营提交失败 addons {err_ext}')
        else:
            logger.info(f'辅营提交成功 addons_2')
        # logger.debug(addons_2)
        # time.sleep(3)
        # payment_form_data = vz_agent_helper.get_form_data(addons_2)
        # logger.debug(payment_form_data)
        # payment_2 = self.client.payment(dlst_expiry='aaa')
        payment_2 = self.client.payment(
            max_retries=3,
            pre_call=self.set_proxy,
            exceptions=(
                # curl_cffi.requests.exceptions.Timeout,
                curl_cffi.requests.exceptions.HTTPError,
                curl_cffi.requests.exceptions.ConnectionError,
            ),
        )
        if '预订页面 - 确认选择' not in payment_2:
            err_ext = vz_agent_helper.get_error_msg(payment_2)
            logger.warning(f'{payment_2} 支付提交失败 payment {err_ext}')
            raise Exception(f'支付提交失败 payment {err_ext}')
        else:
            logger.info(f'支付提交成功 payment_2')

        if 'The server was too busy to process your request' in payment_2:
            raise Exception('The server was too busy to process your request')

        # time.sleep(1)
        confirm_2 = self.client.confirm(
            passengers=passengers,
            max_retries=3,
            pre_call=self.set_proxy,
            exceptions=(
                # curl_cffi.requests.exceptions.Timeout,
                curl_cffi.requests.exceptions.HTTPError,
                curl_cffi.requests.exceptions.ConnectionError,
            ),
        )
        if '预订页面 -旅程' not in confirm_2:
            err_ext = vz_agent_helper.get_error_msg(confirm_2)
            logger.warning(f'{confirm_2} 订单确认失败 confirm {err_ext}')
            raise Exception(f'订单确认失败 confirm {err_ext}')
        else:
            logger.info(f'订单确认成功 confirm_2')

        time.sleep(random.randint(1, 3))
        # for i in range(3):
        process_form_data = vz_agent_helper.get_form_data(confirm_2)
        processing_2 = self.client.processing(form_data=process_form_data)
        if '预订页面 -旅程' not in processing_2:
            err_ext = vz_agent_helper.get_error_msg(processing_2)
            logger.warning(f'{processing_2} PNR生成失败 processing {err_ext}')
            raise Exception(f'PNR生成失败 processing {err_ext}')
        else:
            logger.info(f'PNR生成成功 processing_2')
        # logger.debug(processing_2)
        result = vz_agent_helper.parse_book_result(html=processing_2, airline_code=self.airline_code)
        logger.info(f'预订结果：{result}')

        return result


def get_account():
    fare_sdk = hy_sdks.get_sub_sdk('flight_fare', settings.FLIGHT_FARE_URL)
    result = fare_sdk.get_airline_account(airline_code='VZ')
    if result and result.get('code') == ApiCodes.SUCCESS.value and result.get('data'):
        return {
            'username': result['data']['username'],
            'password': CipherUtils.aes_decrypt(key=settings.AES_KEY, ciphertext=result['data']['password']),
        }
    else:
        raise Exception('航空公司账号不存在')


def run_verify_book(params: dict):
    from commons.extensions.logger_extras import log_uid, log_server_name

    result = {
        'task_info': copy.deepcopy(params),
        'data': None,
        'error': {'code': ApiCodes.UNKNOWN.value, 'message': ApiCodes.UNKNOWN.label},
    }
    account = get_account()
    airline_code = params['airline_code']
    sv = VZAgentService(username=account['username'], password=account['password'], airline_code=params['airline_code'])
    try:

        if not log_server_name.get():
            log_server_name.set('vz_crawler')
        log_uid.set(params['unique_id'])

        sv.set_proxy()
        sv.client.session.timeout = 120
        sv.login()
        search_rs = sv.search(
            dep=params['dep_airport_code'],
            arr=params['arr_airport_code'],
            date=params['dep_date'],
            adult=params['adult'],
            child=params['child'],
            infant=params['infant'],
            currency_code=params['currency_code'],
        )
        if not search_rs or not search_rs['results']:
            return ApiCodes.BOOK_SEARCH_FAIL.raise_error()

        flight = [f for f in search_rs['results'] if f['trips'][0]['flight_nos'][0] == params['flight_no']]
        if flight:
            flight = flight[0]
        else:
            return ApiCodes.BOOK_FLIGHT_NOT_FOUND.raise_error(ext_msg=params['flight_no'])
        logger.debug(flight)
        src_total = ceil(params['src_adult_base'] + params['src_adult_tax'])
        fare_total = ceil(flight['trips'][0]['fares']['adult']['base'] + flight['trips'][0]['fares']['adult']['tax'])
        if fare_total > src_total:
            return ApiCodes.BOOK_PRICE_ERROR.raise_error()
        logger.debug(f'src_total: {src_total}, fare_total: {fare_total}')
        logger.debug(flight)
        # data['flight'] = flight
        fare_key = flight['extra']['fare_key']
        ticket_num = int(fare_key.split(',')[0])
        if ticket_num < params['adult'] + params['child'] + params['infant']:
            # return ApiCodes.BOOK_TICKET_NOT_ENOUGH.raise_error()
            logger.warning(
                f'票数不足 {params["adult"] + params["child"] + params["infant"]} < {ticket_num}, fare_key:{fare_key}, 尝试预订'
            )
            # return ApiCodes.BOOK_TICKET_NOT_ENOUGH.raise_error()
        passengers = params['passengers']
        book_rs = sv.book(fare_key=fare_key, passengers=passengers)
        # data['book'] = book_rs
        result['data'] = {'flight': flight, 'book': book_rs, 'account_name': account['username']}
        result['error']['code'] = ApiCodes.SUCCESS.value
        result['error']['message'] = ApiCodes.SUCCESS.label
    except Exception as e:
        if len(e.args) > 1:
            result['error'] = {'code': e.args[0], 'message': e.args[1]}
            logger.warning(e)
        else:
            result['error'] = {'code': ApiCodes.UNKNOWN.value, 'message': str(e)}
            logger.exception(e)
    finally:
        result['task_info']['host'] = sv.client.domain
        result['task_info']['proxy'] = list(sv.client.session.proxies.values())[0] if sv.client.session.proxies else ''
    # finally:
    #     task_end_time = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")

    #     try:
    #         logger.bind(write_tag="elasticsearch").info(
    #             '',
    #             server_name="vz_verify_book_worker",
    #             unique_id=str(log_uid),
    #             cost_time=time.time() - start_time,
    #             code=result['error']['code'],
    #             message=result['error']['message'],
    #             result=orjson.dumps(result, default=str).decode('utf-8'),
    #             **params,
    #         )
    #     except Exception as e:
    #         logger.warning(e)
    return result


def run_scan_book(params: dict):
    result = {
        'task_info': copy.deepcopy(params),
        'data': None,
        'error': {'code': ApiCodes.UNKNOWN.value, 'message': ApiCodes.UNKNOWN.label},
    }
    vz_search = vz_search_service.VZSearchService()
    search_params = {
        "unique_id": params['unique_id'],
        "dep_airport_code": params['dep_airport_code'],
        "arr_airport_code": params['arr_airport_code'],
        "dep_date": params['dep_date'],
        "return_date": "",
        "trip_type": "ow",
        "airline_code": params['airline_code'],
        "task_key": f"{params['airline_code']}-{params['dep_airport_code']}-{params['arr_airport_code']}-{params['dep_date']}",
        "currency_code": params['currency_code'],
        "adult": params['adult'],
        "child": params['child'],
        "infant": params['infant'],
    }
    flight = None
    has_lower = False
    for _ in range(settings.SCAN_TIMES):
        try:
            vz_search.set_proxy(proxy_group='default', airline_code='VZ')
            search_result = vz_search.run_search(search_params)
            logger.debug(search_result)
            # 查询失败
            if search_result['error']['code'] != ApiCodes.SUCCESS.value:
                result['error'] = search_result['error']
                logger.warning(f'查询失败 {search_result["error"]}')
                continue

            flight = [
                f for f in search_result['data']['results'] if f['trips'][0]['flight_nos'][0] == params['flight_no']
            ]
            if not flight:
                result['error']['code'] = ApiCodes.BOOK_FLIGHT_NOT_FOUND.value
                result['error']['message'] = ApiCodes.BOOK_FLIGHT_NOT_FOUND.label
                continue

            flight = flight[0]
            if ceil(flight['trips'][0]['fares']['adult']['base']) < ceil(params['src_adult_base']):
                has_lower = True
                break
            else:
                result['error']['code'] = ApiCodes.BOOK_SCAN_NO_LOWER.value
                result['error']['message'] = (
                    ApiCodes.BOOK_SCAN_NO_LOWER.label + f'余票:{flight["trips"][0]["fares"]["adult"]["quantity"]}'
                )
        finally:
            logger.info(f'无低价，1秒后重试')
            time.sleep(1)
    logger.debug(flight)
    # 票价低于上次预订，则尝试预订更低价格
    if has_lower:
        result = run_verify_book(params)
    # else:
    #     result['error']['code'] = ApiCodes.BOOK_SCAN_NO_LOWER.value
    #     result['error']['message'] = (
    #         ApiCodes.BOOK_SCAN_NO_LOWER.label + f'余票:{flight["trips"][0]["fares"]["adult"]["quantity"]}'
    #     )
    return result


def run_hood_book(params: dict):
    result = {
        'task_info': copy.deepcopy(params),
        'data': None,
        'error': {'code': ApiCodes.UNKNOWN.value, 'message': ApiCodes.UNKNOWN.label},
    }
    vz_search = vz_search_service.VZSearchService()
    search_params = {
        "unique_id": params['unique_id'],
        "dep_airport_code": params['dep_airport_code'],
        "arr_airport_code": params['arr_airport_code'],
        "dep_date": params['dep_date'],
        "return_date": "",
        "trip_type": "ow",
        "airline_code": params['airline_code'],
        "task_key": f"{params['airline_code']}-{params['dep_airport_code']}-{params['arr_airport_code']}-{params['dep_date']}",
        "currency_code": params['currency_code'],
        "adult": params['adult'],
        "child": params['child'],
        "infant": params['infant'],
    }
    flight = None
    has_lower = False
    end_time = datetime.now() + timedelta(minutes=params['keep_minutes'] if params['keep_minutes'] else 5)

    while datetime.now() <= end_time:
        logger.info(f'开始压位出票，当前时间：{datetime.now()}, 结束时间：{end_time}')
        try:
            search_result = vz_search.run_search(search_params)
            logger.debug(search_result)
            # 查询失败
            if search_result['error']['code'] != ApiCodes.SUCCESS.value:
                result['error'] = search_result['error']
                logger.warning(f'查询失败 {search_result["error"]}')
                continue

            flight = [
                f for f in search_result['data']['results'] if f['trips'][0]['flight_nos'][0] == params['flight_no']
            ]
            if not flight:
                result['error']['code'] = ApiCodes.BOOK_FLIGHT_NOT_FOUND.value
                result['error']['message'] = ApiCodes.BOOK_FLIGHT_NOT_FOUND.label
                continue

            flight = flight[0]
            if ceil(flight['trips'][0]['fares']['adult']['base']) <= ceil(params['src_adult_base']) + 1:
                has_lower = True
                break
            else:
                result['error']['code'] = ApiCodes.BOOK_SCAN_NO_LOWER.value
                result['error']['message'] = (
                    ApiCodes.BOOK_SCAN_NO_LOWER.label + f'余票:{flight["trips"][0]["fares"]["adult"]["quantity"]}'
                )
            logger.info(
                f'当前票价：{ceil(flight["trips"][0]["fares"]["adult"]["base"])}, 目标票价：<= {ceil(params["src_adult_base"])}'
            )

        finally:
            logger.info(f'无低价，1秒后重试')
            time.sleep(1)
    logger.debug(flight)
    # 票价低于上次预订，则尝试预订更低价格
    if has_lower:
        logger.info(f'找到符合条件的航班，开始预订 {flight}')
        result = run_verify_book(params)
    else:
        logger.info(f'压位出票失败，结束')

    return result
