import importlib
import random
from loguru import logger
from app.config import settings


def parse_proxy_string(proxy_string: str) -> dict:
    """
    将代理字符串转换为字典格式，自动判断是否包含用户名和密码
    """
    # 拆分协议和剩余部分
    protocol, rest = proxy_string.split("://")

    if "@" in rest:
        # 包含用户名和密码的情况
        credentials, server = rest.split("@")
        username, password = credentials.split(":")
        return {"server": f"{protocol}://{server}", "username": username, "password": password}
    else:
        # 不包含用户名和密码的情况
        return {"server": f"{protocol}://{rest}", "username": "", "password": ""}


def format_proxy_dict(proxy_dict: dict) -> str:
    """
    将代理字典转换回字符串格式，自动判断是否包含用户名和密码
    """
    # 提取信息
    protocol_server = proxy_dict.get("server")
    username = proxy_dict.get("username")
    password = proxy_dict.get("password")

    # 组装字符串
    if username and password:
        # 有用户名和密码的情况
        return f"{protocol_server.split('://')[0]}://{username}:{password}@{protocol_server.split('://')[1]}"
    else:
        # 无用户名和密码的情况
        return protocol_server
