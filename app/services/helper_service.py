from loguru import logger
from app.consts.currency import CurrencyCodes
from app.services import vz_search_service
from commons import sdks
from commons.sdks.base import SdkClient
from app.config import settings
import pandas as pd


def do_currency_compare(dep, arr, date, fnb):
    from commons.extensions.logger_extras import log_uid

    log_uid.set(f'{dep}-{arr}-{date}')

    currency_codes = CurrencyCodes.mappings()
    logger.debug(currency_codes)

    result = []
    pay_center_sdk = SdkClient(host=settings.PAY_CENTER_URL)

    for currency_code, currency_name in currency_codes.items():
        # if airport["currency_cd"] != currency_code and currency_code != CurrencyCodes.KRW.value:
        #     logger.warning(f'不支持 {currency_code} 预订')
        #     continue
        vz_search = vz_search_service.VZSearchService()

        search_result = vz_search.run_search(
            {
                'airline_code': 'VZ',
                'dep_airport_code': dep,
                'arr_airport_code': arr,
                'dep_date': date,
                'currency_code': currency_code,
            }
        )

        if not search_result or not search_result.get('data') or not search_result['data'].get('results'):
            logger.warning(f'不支持 {currency_code} 预订')
            continue

        logger.debug(search_result)

        flight_info = search_result['data']['results'][0]['trips'][0]
        book_currency_code = search_result['data']['exchange']['currency_code']
        tmp = {
            '航线': f'{dep}_{arr}_{date}',
            '航班': ','.join(flight_info['flight_nos']),
            '舱位': ','.join(flight_info['cabin_codes']),
            '余票': flight_info['fares']['adult']['quantity'],
            '运价key': search_result['data']['results'][0]['extra']['fare_key'],
            '币种': f'{book_currency_code}({CurrencyCodes(book_currency_code).label})',
            '原票价': round(float(flight_info['fares']['adult']['base']), 2),
            '原税费': round(float(flight_info['fares']['adult']['tax']), 2),
        }
        if book_currency_code == 'CNY':
            tmp['票价(人民币)'] = tmp['原票价']
            tmp['税(人民币)'] = tmp['原税费']
            tmp['总价(人民币)'] = tmp['原票价'] + tmp['原税费']
            tmp['汇率'] = 1
        else:

            resp = pay_center_sdk.send(
                request=sdks.pay_center.exchange_rate.GetExchangeRateRequest(
                    from_currency=book_currency_code, to_currency="CNY"
                )
            )
            rate = resp['data']['rate']
            tmp['票价(人民币)'] = round(tmp['原票价'] * rate, 2)
            tmp['税(人民币)'] = round(tmp['原税费'] * rate, 2)
            tmp['总价(人民币)'] = round(tmp['票价(人民币)'] + tmp['税(人民币)'], 2)
            tmp['汇率'] = round(rate, 6)

        result.append(tmp)
    df = pd.DataFrame(result)
    df.to_csv(f'{dep}-{arr}-{date}.csv', index=False)
    # logger.info('*' * 30)
    # for r in result:
    #     logger.info(
    #         f'{r["fnb"]}\t{r["cabin"]}\t{r["currency_code"]}:\t{r["adult_base"]}\t{r["adult_tax"]}\tCNY:\t{round(r["cny_adult_base"]+r["cny_adult_tax"],2)}\t{round(r["cny_adult_base"],2)}\t{round(r["cny_adult_tax"],2)}\t{round(r["exchange_rate"],5)}'
    #     )
