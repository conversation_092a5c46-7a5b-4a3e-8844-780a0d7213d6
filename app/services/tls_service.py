from typing import Union
import curl_cffi
from loguru import logger


from commons import sdks as hy_sdks
from commons.cores.base_client import BaseClientNew


async def forward_by_curl_cffi(method: str, url: str, proxy_str: str = None, timeout: Union[int, float] = 60, **kwargs):
    client = BaseClientNew(host=None, timeout=timeout)

    if proxy_str:
        proxy_auth = None
        proxies = {'http': proxy_str, 'https': proxy_str}
        if '@' in proxy_str:
            proxy = proxy_str.split(':')[0] + '://' + proxy_str.split('@')[-1]
            proxies = {'http': proxy, 'https': proxy}
            proxy_auth = tuple(proxy_str.split('@')[0].split('//')[-1].split(':'))
        kwargs['proxies'] = proxies
        # kwargs['proxy'] = proxy
        kwargs['proxy_auth'] = proxy_auth
    logger.debug(f'kwargs: {kwargs}')
    data = kwargs.pop('data', None)
    content_type = ''
    if 'headers' in kwargs:
        for k, v in kwargs['headers'].items():
            if k.lower() == 'content-type':
                content_type = v
                break

    if isinstance(data, dict):
        if content_type.lower() == 'application/json':
            logger.debug(f'send json data: {data}')
            kwargs['json'] = data
        else:
            logger.debug(f'send data: {data}')
            kwargs['data'] = data
    response = None
    try:
        response = await client.request_async(method, url, **kwargs)
        result = {
            'response': response,
            'status_code': client.lastest_response_status_code,
            'headers': client.lastest_response_headers,
            'cookies': client.lastest_response_cookies,
            'exception': None,
        }
    except Exception as e:
        result = {
            'response': response,
            'status_code': client.lastest_response_status_code,
            'headers': client.lastest_response_headers,
            'cookies': client.lastest_response_cookies,
            'exception': str(e),
        }

    return result
