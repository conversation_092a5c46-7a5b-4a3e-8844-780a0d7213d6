import asyncio
import copy
from datetime import datetime, timedelta
from math import ceil
import random
import re
import time
import aiohttp
from loguru import logger
import orj<PERSON>
import pytz

from app.clients import vz_consts
from app.clients.vz_client import VZClient
from app.sdks.flight_fare_sdk import FlightFareSdk
from app.config import settings
from app.services import proxy_service

import re
from bs4 import BeautifulSoup

from app.services.vz_search_service import VZSearchService
from commons import sdks
from commons.consts.api_codes import ApiCodes
from commons.sdks.base import SdkClient

# TODO 待实现


class VZBookService(VZSearchService):
    def __init__(self) -> None:
        super().__init__()
        # vz默认用二维码
        self.pay_method = 'QR Payment'

    def run_book(self, task_data: dict):
        result = {
            "task_info": copy.deepcopy(task_data),
            "error": {"code": ApiCodes.UNKNOWN.value, "message": ApiCodes.UNKNOWN.label},
        }
        try:
            from commons.extensions.logger_extras import log_uid

            task_key = task_data.get('task_key')
            if not task_key and not str(log_uid):
                task_key = f'{task_data["crawler_code"].upper()}-{task_data["dep_airport_code"]}-{task_data["arr_airport_code"]}-{task_data["dep_date"]}'
                log_uid.set(task_key)

            self.set_proxy(proxy_group='book')

            if task_data.get('currency_code'):
                self.currency = task_data['currency_code']

            pass
        except Exception as e:
            logger.exception(e)
        finally:
            pass
        return result


def build_booking_form(booking_code: str, contact: dict, passengers: list, ssr_r: dict):
    passengers = covert_passengers(passengers=passengers)
    data = {
        'code': booking_code,
        'first_name': passengers[0]['first_name'],
        'last_name': passengers[0]['last_name'],
        'country': 'CHN',
        'area_code': '+86',
        'phone': contact['mobile'],
        'email': contact['email'],
        'pax_adult_id[]': ['' for _ in range(len(passengers))] if len(passengers) > 1 else '',
        'pax_adult_funid[]': ['' for _ in range(len(passengers))] if len(passengers) > 1 else '',
        'pax_adult_gender[]': [p['sex2'] for p in passengers] if len(passengers) > 1 else passengers[0]['sex2'],
        'pax_adult_first_name[]': (
            [p['first_name'] for p in passengers] if len(passengers) > 1 else passengers[0]['first_name']
        ),
        'pax_adult_last_name[]': (
            [p['last_name'] for p in passengers] if len(passengers) > 1 else passengers[0]['last_name']
        ),
        'pax_adult_dob[]': [p['birthday2'] for p in passengers] if len(passengers) > 1 else passengers[0]['birthday2'],
        'pax_adult_nationality[]': (
            [p['country2'] for p in passengers] if len(passengers) > 1 else passengers[0]['country2']
        ),
        'g-recaptcha-response': '',
        'applyVoucher': 'false',
        'serial': '',
        'pin': '',
        'password': '',
        'free_services[0][]': '',
    }
    departure_ancillary = ssr_r['data']['departureAncillary']
    for i in range(len(passengers)):
        # 'paxes_inf[0][ancillary][0][Baggage]': 'Baggage',
        # 'paxes_inf[0][ancillary][0][Cabin Baggage]': 'Cabin Baggage',
        # 'paxes_inf[0][ancillary][0][Meal]': 'Meal',
        # 'paxes_inf[0][ancillary][0][Priority]': 'Priority',
        # 'paxes_inf[0][ancillary][0][Vip Lounge]': 'Vip Lounge',
        # 'paxes_inf[0][ancillary][0][Special Service]': 'Special Service',
        for k, v in departure_ancillary.items():
            # logger.debug(k)
            if k in ['Skyboss', 'Special change', 'Deluxe ancillary']:
                continue
            data[f'paxes_inf[{i}][ancillary][0][{k}]'] = k
        p = passengers[i]
        data.update(
            {
                f'paxes_inf[{i}][type]': p['passenger_type'],
                f'paxes_inf[{i}][first_name]': p['first_name'],
                f'paxes_inf[{i}][last_name]': p['last_name'],
                f'paxes_inf[{i}][gender]': p['sex2'],
                f'paxes_inf[{i}][dob]': p['birthday2'],
                f'paxes_inf[{i}][passport]': '',
                f'paxes_inf[{i}][nationality]': p['country2'],
                f'paxes_inf[{i}][funid]': '',
            }
        )

        # 'paxes_inf[1][ancillary][0][Baggage]': 'Baggage',
        # 'paxes_inf[1][ancillary][0][Cabin Baggage]': 'Cabin Baggage',
        # 'paxes_inf[1][ancillary][0][Meal]': 'Meal',
        # 'paxes_inf[1][ancillary][0][Priority]': 'Priority',
        # 'paxes_inf[1][ancillary][0][Vip Lounge]': 'Vip Lounge',
        # 'paxes_inf[1][ancillary][0][Special Service]': 'Special Service',
        # 'paxes_inf[1][type]': 'adult',
        # 'paxes_inf[1][first_name]': 'wang',
        # 'paxes_inf[1][last_name]': 'xing',
        # 'paxes_inf[1][gender]': 'Male',
        # 'paxes_inf[1][dob]': '09/07/1988',
        # 'paxes_inf[1][passport]': '',
        # 'paxes_inf[1][nationality]': 'CHN',
        # 'paxes_inf[1][funid]': '',
        # 'paxes_inf[2][ancillary][0][Baggage]': 'Baggage',
        # 'paxes_inf[2][ancillary][0][Cabin Baggage]': 'Cabin Baggage',
        # 'paxes_inf[2][ancillary][0][Meal]': 'Meal',
        # 'paxes_inf[2][ancillary][0][Priority]': 'Priority',
        # 'paxes_inf[2][ancillary][0][Vip Lounge]': 'Vip Lounge',
        # 'paxes_inf[2][ancillary][0][Special Service]': 'Special Service',
        # 'paxes_inf[2][type]': 'adult',
        # 'paxes_inf[2][first_name]': 'liu',
        # 'paxes_inf[2][last_name]': 'fang',
        # 'paxes_inf[2][gender]': 'Female',
        # 'paxes_inf[2][dob]': '07/05/1999',
        # 'paxes_inf[2][passport]': '',
        # 'paxes_inf[2][nationality]': 'CHN',
        # 'paxes_inf[2][funid]': '',
    data.update(
        {
            'selected_addon_inf[0][0][title]': 'Seat',
            'selected_addon_inf[0][0][key]': 'Seat',
            'selected_addon_inf[0][0][totalAmount]': '0',
            'selected_addon_inf[0][0][baseAmount]': '0',
            'selected_addon_inf[0][0][taxAmount]': '0',
            'selected_addon_inf[0][0][quantity]': '0',
            'selected_addon_inf[0][1][title]': 'Baggages',
            'selected_addon_inf[0][1][key]': 'Baggage',
            'selected_addon_inf[0][1][totalAmount]': '0',
            'selected_addon_inf[0][1][baseAmount]': '0',
            'selected_addon_inf[0][1][taxAmount]': '0',
            'selected_addon_inf[0][1][quantity]': '0',
            'selected_addon_inf[0][2][title]': 'Cabin Baggage',
            'selected_addon_inf[0][2][key]': 'Cabin Baggage',
            'selected_addon_inf[0][2][totalAmount]': '0',
            'selected_addon_inf[0][2][baseAmount]': '0',
            'selected_addon_inf[0][2][taxAmount]': '0',
            'selected_addon_inf[0][2][quantity]': '0',
            'selected_addon_inf[0][3][title]': 'Hot meals',
            'selected_addon_inf[0][3][key]': 'Meal',
            'selected_addon_inf[0][3][totalAmount]': '0',
            'selected_addon_inf[0][3][baseAmount]': '0',
            'selected_addon_inf[0][3][taxAmount]': '0',
            'selected_addon_inf[0][3][quantity]': '0',
            'selected_addon_inf[0][4][title]': 'Priority Checkin',
            'selected_addon_inf[0][4][key]': 'Priority',
            'selected_addon_inf[0][4][totalAmount]': '0',
            'selected_addon_inf[0][4][baseAmount]': '0',
            'selected_addon_inf[0][4][taxAmount]': '0',
            'selected_addon_inf[0][4][quantity]': '0',
            'selected_addon_inf[0][5][title]': 'Vip Lounge',
            'selected_addon_inf[0][5][key]': 'Vip Lounge',
            'selected_addon_inf[0][5][totalAmount]': '0',
            'selected_addon_inf[0][5][baseAmount]': '0',
            'selected_addon_inf[0][5][taxAmount]': '0',
            'selected_addon_inf[0][5][quantity]': '0',
            'selected_addon_inf[0][6][title]': 'Special Service',
            'selected_addon_inf[0][6][key]': 'Special Service',
            'selected_addon_inf[0][6][totalAmount]': '0',
            'selected_addon_inf[0][6][baseAmount]': '0',
            'selected_addon_inf[0][6][taxAmount]': '0',
            'selected_addon_inf[0][6][quantity]': '0',
            'selected_addon_inf[0][7][title]': 'VAT',
            'selected_addon_inf[0][7][key]': 'tax',
            'selected_addon_inf[0][7][totalAmount]': '0',
            'selected_addon_inf[0][7][baseAmount]': '0',
            'selected_addon_inf[0][7][taxAmount]': '0',
            'selected_addon_inf[0][7][quantity]': '1',
            'selected_other_services[0][0][title]': 'Travel Insurance',
            'selected_other_services[0][0][key]': 'Insurance',
            'selected_other_services[0][0][totalAmount]': '0',
            'selected_other_services[0][0][baseAmount]': '0',
            'selected_other_services[0][0][taxAmount]': '0',
            'selected_other_services[0][0][quantity]': '0',
        }
    )

    # 添加联系人
    # booking_data += parse_contact(contact=contact)
    # booking_data.update(parse_passengers(passengers=passengers, ssr_r=ssr_r))

    return data


def str_to_list(passenger_data, key, value):
    if key in passenger_data:
        if not isinstance(passenger_data[key], list):
            passenger_data[key] = [passenger_data[key]]
        passenger_data[key].append(value)
    else:
        passenger_data[key] = value


def covert_passengers(passengers: list):
    for p in passengers:
        brithday = datetime.strptime(p['birthday'], '%Y-%m-%d').strftime('%d/%m/%Y')
        country_code = vz_consts.COUNTRY_MAP[p['country']]['code']
        p['birthday2'] = brithday
        p['country2'] = country_code
        p['sex2'] = p['sex'].capitalize()
    return passengers


def parse_passengers(passengers: list, ssr_r: dict):
    passenger_data = {}

    departure_ancillary = ssr_r['data']['departureAncillary']
    for idx in range(len(passengers)):
        # insurance_price = float(insurance['unit_price'])
        # insurance_total_amount += insurance_price

        passenger = passengers[idx]
        brithday = datetime.strptime(passenger['birthday'], '%Y-%m-%d').strftime('%d/%m/%Y')
        country_code = vz_consts.COUNTRY_MAP[passenger['country']]['code']

        if passenger['passenger_type'] == 'adult':
            str_to_list(passenger_data=passenger_data, key='pax_adult_id[]', value='')
            str_to_list(passenger_data=passenger_data, key='pax_adult_funid[]', value='')
            str_to_list(passenger_data=passenger_data, key='pax_adult_gender[]', value=passenger['sex'].capitalize())
            str_to_list(passenger_data=passenger_data, key='pax_adult_first_name[]', value=passenger['first_name'])
            str_to_list(passenger_data=passenger_data, key='pax_adult_last_name[]', value=passenger['last_name'])
            str_to_list(passenger_data=passenger_data, key='pax_adult_dob[]', value=brithday)
            str_to_list(passenger_data=passenger_data, key='pax_adult_nationality[]', value=country_code)
        elif passenger['passenger_type'] == 'child':
            str_to_list(passenger_data=passenger_data, key='pax_child_id[]', value='')
            str_to_list(passenger_data=passenger_data, key='pax_child_gender[]', value=passenger['sex'].capitalize())
            str_to_list(passenger_data=passenger_data, key='pax_child_first_name[]', value=passenger['first_name'])
            str_to_list(passenger_data=passenger_data, key='pax_child_last_name[]', value=passenger['last_name'])
            str_to_list(passenger_data=passenger_data, key='pax_child_dob[]', value=brithday)
            str_to_list(passenger_data=passenger_data, key='pax_child_nationality[]', value=country_code)

        for k, v in departure_ancillary.items():
            if not v:
                continue
            passenger_data[f'paxes_inf[{idx}][ancillary][0][{k}]'] = k

        passenger_data[f'paxes_inf[{idx}][type]'] = passenger['passenger_type']
        passenger_data[f'paxes_inf[{idx}][first_name]'] = passenger['first_name']
        passenger_data[f'paxes_inf[{idx}][last_name]'] = passenger['last_name']
        passenger_data[f'paxes_inf[{idx}][gender]'] = passenger['sex'].capitalize()
        passenger_data[f'paxes_inf[{idx}][dob]'] = brithday
        passenger_data[f"paxes_inf[{idx}][passport]"] = ""
        passenger_data[f'paxes_inf[{idx}][nationality]'] = country_code
        passenger_data[f'paxes_inf[{idx}][funid]'] = ''

    # passenger_data.append(('selected_other_services[0][0][totalAmount]', insurance_total_amount))
    # passenger_data.append(('selected_other_services[0][0][baseAmount]', insurance_total_amount))
    # passenger_data.append(('selected_other_services[0][0][taxAmount]', '0'))
    # passenger_data.append(('selected_other_services[0][0][quantity]', len(passengers)))
    # 不购买保险，统一传0
    # passenger_data.append(('selected_other_services[0][0][totalAmount]', 0))
    # passenger_data.append(('selected_other_services[0][0][baseAmount]', 0))
    # passenger_data.append(('selected_other_services[0][0][taxAmount]', '0'))
    # passenger_data.append(('selected_other_services[0][0][quantity]', 0))

    return passenger_data


def parse_contact(contact: dict):
    contact_data = [
        ('first_name', contact['first_name']),
        ('last_name', contact['last_name']),
        ('phone', contact['mobile']),
        ('email', contact['email']),
    ]
    return contact_data


def compute_booking_expiry_time(flight: dict, expired_booking: int):
    # 获取航班起飞地的时区
    target_timezone = pytz.timezone(flight['departure_date']['timezone'])

    # Get the current time in the target timezone
    tag_zone_curr_time = datetime.now(target_timezone).replace(tzinfo=None)
    system_time = datetime.now()  # 当前系统时区时间
    zone_diff = (system_time - tag_zone_curr_time).total_seconds() / 3600
    # Add 30 minutes to the current time
    expired_time = (tag_zone_curr_time + timedelta(minutes=expired_booking)).strftime('%Y-%m-%d %H:%M:%S')
    real_expired_time = (datetime.strptime(expired_time, '%Y-%m-%d %H:%M:%S') + timedelta(hours=zone_diff)).strftime(
        '%Y-%m-%d %H:%M:%S'
    )
    return expired_time, real_expired_time
