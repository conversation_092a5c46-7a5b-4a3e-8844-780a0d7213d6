import asyncio
from datetime import datetime
import time
from typing import Dict, List, Optional

from loguru import logger
import orjson

from app.config import ASYNC_REDIS_CFG, settings
from app.models.host import HostOrm
from app.models.proxy import ProxyOrm
from app.services import proxy_service
from commons.consts.common_status import EnableStatus
from commons.extensions.redis_extras import AsyncRedisPool


async def check_proxy(proxy: dict, timeout: int = 30, semaphore: asyncio.Semaphore = None) -> Optional[dict]:
    """检查代理可用性
    Args:
        proxy: 代理信息字典，包含 proxy_str, site, host, country_code 等信息
        timeout: 超时时间
        semaphore: 并发控制信号量
    Returns:
        dict: 检查通过返回代理信息，失败返回None
    """
    from curl_cffi import requests
    from commons.extensions.logger_extras import log_uid

    if not semaphore:
        semaphore = asyncio.Semaphore(1)
    log_uid.set(f'{proxy["ip"]}-{proxy["port"]}')
    domain = 'www.google.com'
    #if proxy['country_code'] != 'CN':
    #    domain = 'th.vietjetair.com'

    async with semaphore:
        headers = {
            "Host": domain,
            # "authority": 'www.baidu.com',
            "accept": "application/json, text/javascript, */*; q=0.01",
            "accept-language": "zh-CN,zh;q=0.9",
            "cache-control": "no-cache",
            "dnt": "1",
            "pragma": "no-cache",
            "sec-ch-ua": '"Chromium";v="116", "Not)A;Brand";v="24", "Google Chrome";v="116"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"macOS"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-requested-with": "XMLHttpRequest",
        }

        logger.info('开始检查代理')
        start_time = time.time()
        url = f'https://{domain}/'

        # parsed_proxy = proxy_service.parse_proxy_string(proxy["proxy_str"])
        proxy_str = f'{proxy["protocol"]}://{proxy["ip"]}:{proxy["port"]}'  
        proxies = {"https": proxy_str, "http": proxy_str}
        proxy_auth = None
        if proxy.get('username') and proxy.get('password'):
            proxy_auth = (proxy.get('username'), proxy.get('password'))
            
        try:
            #return proxy
            async with requests.AsyncSession() as s:
                s.proxies = proxies
                if proxy_auth:
                    s.proxy_auth = proxy_auth
                resp = await s.get(url, headers=headers, timeout=timeout, verify=False)
                if resp.status_code == 200:
                    proxy.update({
                        'cost_time': time.time() - start_time,
                        'primary_ip': resp.primary_ip,
                        'last_check_time': datetime.now()
                    })
                    logger.debug(f'检查通过: {proxy}')
                    return proxy
                else:
                    logger.warning(f'代理响应异常: {resp.status_code}')
                    return None
        except Exception as e:
            logger.warning(f'代理检查失败: {str(e)} {proxy_auth}')
            return None


async def set_active_list(results: list):
    """按国家二字码分组存储可用代理"""
    country_proxies: Dict[str, Dict[str, Dict[str, List]]] = {}
    
    for proxy in results:
        if proxy:  # 只处理检查通过的代理
            country_code = proxy['country_code'] or 'unknown'
            # proxy_str = proxy['proxy_str']
            
            # 初始化数据结构
            if country_code not in country_proxies:
                country_proxies[country_code] = {}
           
            # 相同国家、站点、代理，选择最低延迟
            proxy_str = f'{proxy["protocol"]}://{proxy["ip"]}:{proxy["port"]}'
            if proxy_str not in country_proxies[country_code] or \
               country_proxies[country_code][proxy_str]['cost_time'] > proxy['cost_time']:
                country_proxies[country_code][proxy_str] = proxy

    logger.debug(f'按国家分组的代理池: {country_proxies}')
    
    # 存入Redis
    async with AsyncRedisPool(**ASYNC_REDIS_CFG) as redis:
        for country_code, proxies in country_proxies.items():
            
            key = f'{country_code}_{settings.PROXY_POOL_SUFFIX}'.lower()
            value = orjson.dumps(list(proxies.values()), default=str).decode("utf-8")
            await redis.set(key, value, ex=60 * 60 * 24)
            logger.info(f'代理入池: {key} 数量: {len(proxies)}')

async def set_host_list():
    """设置有效的host列表"""
    hosts = await HostOrm.get_all_async(HostOrm.status == EnableStatus.ENABLED.value)
    mp = {}
    for host in hosts:
        key = f'{host["airline_code"]}_{host["domain"]}'
        if key not in mp:
            mp[key] = []
        mp[key].append(host["host"])
    logger.info(f'有效的host列表: {mp}')
    async with AsyncRedisPool(**ASYNC_REDIS_CFG) as redis:
        for key, hosts in mp.items():
            value = orjson.dumps(list(set(hosts)), default=str).decode("utf-8")
            await redis.set(key, value, ex=60 * 60 * 24)
            logger.info(f'host入池: {key} 数量: {len(hosts)}')
    

async def run_check(interval: int, concurrency: int):
    from commons.extensions.logger_extras import log_uid

    while True:
        try:
            log_uid.set('main')
            tasks = []

            # 控制并发
            semaphore = asyncio.Semaphore(concurrency)
            
            # 获取未过期的代理
            proxies = await ProxyOrm.get_all_async(ProxyOrm.expire_time > datetime.now())
            for proxy in proxies:
               
                tasks.append(
                    check_proxy(
                        proxy=proxy,
                        timeout=90,
                        semaphore=semaphore
                    )
                )
            
            if not tasks:
                logger.warning("没有找到有效的代理")
                continue
                
            logger.info(f'开始检查 {len(tasks)} 个代理')
            results = await asyncio.gather(*tasks, return_exceptions=True)
            # 过滤掉异常结果
            valid_results = [r for r in results if isinstance(r, dict)]
            await set_active_list(valid_results)
            await set_host_list()
            
        except Exception as e:
            logger.error(f"代理检查过程发生错误: {e}")
            
        finally:
            logger.info(f'休眠 {interval} 秒...')
            await asyncio.sleep(interval)
