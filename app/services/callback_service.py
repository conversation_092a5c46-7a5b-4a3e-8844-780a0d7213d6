import asyncio
import copy
from datetime import datetime, timedelta
from math import ceil
import random
import re
import aiohttp
from loguru import logger
import orjson
from app.sdks.flight_fare_sdk import FlightFareSdk
from app.config import settings
from app.sdks.flight_pre_order_sdk import FlightPreOrderSdk
from commons.consts.api_codes import ApiCodes
from commons.sdks import flight_pre_order
from commons.sdks.base import SdkClient

# todo
# 儿童价、婴儿价先按成人价计算
sdk_client = SdkClient(host=settings.FLIGHT_FARE_URL)


def verify_book_result_callback(result: dict) -> bool:

    callback_url = result['task_info']['callback_url']

    resp = sdk_client.request(method='POST', url=callback_url, json=result)

    return resp


def scan_book_result_callback(result: dict) -> bool:
    callback_url = result['task_info']['callback_url']

    resp = sdk_client.request(method='POST', url=callback_url, json=result)

    return resp


def hood_book_result_callback(result: dict) -> bool:
    callback_url = result['task_info']['callback_url']

    resp = sdk_client.request(method='POST', url=callback_url, json=result)

    return resp
