import asyncio
import copy
from datetime import datetime, timedelta
from math import ceil
import random
import re
import time
import aiohttp
from curl_cffi import <PERSON>urlOpt
from loguru import logger
import orjson

from app.clients.vz_client import VZClient
from app.sdks.flight_fare_sdk import FlightFareSdk
from app.config import settings
from app.services import pre_order_service, proxy_service

import re
from bs4 import BeautifulSoup

from app.services import vz_book_service
from app.services import vz_search_service
from app.services.vz_book_service import VZBookService
from app.services.vz_search_service import VZSearchService
from commons import sdks
from commons.consts.api_codes import ApiCodes
from commons.sdks.base import SdkClient


class VZHoodService(VZBookService):
    def __init__(self) -> None:
        super().__init__()
        self.tag_cabin_level = 'Eco'
        self.vz_client.session.timeout = 90
        # self.vz_client.enable_delay_request = True

    def refresh_csrf_token(self):
        html_content = self.vz_client.index_page(max_retries=3)
        if not html_content:
            raise Exception('获取首页失败')
        csrf_token = get_csrf_token(html_content=html_content)
        if not csrf_token:
            raise Exception('获取CSRF Token失败')
        self.vz_client.session.headers.update({'x-csrf-token': csrf_token})
        # self.vz_client.resolve_value = "th.vietjetair.com:443:52.74.190.199".encode('utf-8')
        # 打开详细调试
        # self.vz_client.session.curl.setopt(CurlOpt.VERBOSE, 1)

    def run_hood(self, task_data: dict):
        result = {
            "task_info": copy.deepcopy(task_data),
            "data": None,
            # 默认占座失败
            "error": {"code": ApiCodes.HOOD_ERROR.value, "message": ApiCodes.HOOD_ERROR.label},
        }
        try:
            from commons.extensions.logger_extras import log_uid

            unique_id = task_data.get('unique_id')
            log_uid.set(unique_id)

            if task_data.get('currency_code'):
                self.currency = task_data['currency_code']
            # 加代理
            self.set_proxy(proxy_group='hood')
            logger.info(f'开始压位:{self.vz_client.session.proxies}')

            # 获取CSRF Token
            self.refresh_csrf_token()
            # self.vz_client.session.headers.update({'x-csrf-token': 'CpINXvTUblL9symFFj3bxSYfJ7tuiwOjA0Gxd6vF'})

            # 参数准备
            trip_type = self.trip_type
            from_where = task_data["dep_airport_code"]
            to_where = task_data["arr_airport_code"]
            start = datetime.strptime(task_data["dep_date"], '%Y-%m-%d').strftime('%d/%m/%Y')
            end = datetime.strptime(task_data["dep_date"], '%Y-%m-%d').strftime('%d/%m/%Y')
            adult_count = task_data.get('adult', 1)
            child_count = task_data.get('child', 0)
            infant_count = task_data.get('infant', 0)
            promo_code = ''
            currency = task_data.get('currency', self.currency)
            exclude_flights = task_data.get('exclude_flights', [])

            # 1. 查询
            search_r = self.vz_client.search(
                trip_type=trip_type,
                from_where=from_where,
                to_where=to_where,
                start=start,
                end=end,
                adult_count=adult_count,
                child_count=child_count,
                infant_count=infant_count,
                promo_code=promo_code,
                currency=currency.lower(),
            )
            # 检查目标航班是否可压位
            if not search_r or not search_r.get('data'):
                result['error']['message'] = '查询失败'
                # call_r = pre_order_service.hood_callback(result)
                return result

            hood_flight = find_hood_flight(
                search_r,
                exclude_flights=exclude_flights,
                tag_cabin_level=self.tag_cabin_level,
                min_quantity=task_data['min_quantity'],
                max_price=task_data.get('max_price', 0),
            )

            if not hood_flight:
                result['error']['code'] = ApiCodes.HOOD_NO_RESULT.value
                result['error']['message'] = ApiCodes.HOOD_NO_RESULT.label
                # call_r = pre_order_service.hood_callback(result)
                return result

            if hood_flight:
                result['data'] = None

                api_uuid = search_r['data']['api_uuid']
                expired_booking = int(search_r['data']['expired_booking'])
                flight = hood_flight['flights'][0]
                expired_time, real_expired_time = vz_book_service.compute_booking_expiry_time(
                    flight=flight, expired_booking=expired_booking
                )
                # 2. 加购物车（获取临时订单号）
                add_r = self.vz_client.add_to_cart(
                    api_uuid=api_uuid,
                    trip_type=trip_type,
                    expired_time=expired_time,
                    departure=hood_flight,
                    seat_code=self.tag_cabin_level,
                )

                if not add_r or 'booking_code' not in add_r:
                    result['error']['message'] = f'获取临时订单失败, {add_r}'
                    # call_r = pre_order_service.hood_callback(result)
                    return result
                booking_code = add_r['booking_code']
                passenger_url = add_r['url']

                logger.debug(booking_code)
                logger.debug(passenger_url)

                # 3. 填单：乘机人填写页（必须请求，否则ssr过不去）
                # passenger_html = self.vz_client._request('GET', passenger_url)
                passenger_html = self.vz_client.touch_passenger(booking_code=booking_code)

                # 3.1 检查购物车状态
                # self.vz_client.check_status(booking_code=booking_code)

                # 3.1 获取国家列表（非必要，已存为常量）
                # countries_r = self.vz_client.get_countries()

                # 3.2 获取ssr（辅营项目列表）
                ssr_r = self.vz_client.get_ssr(booking_code)
                if not ssr_r:
                    result['error']['message'] = f'获取辅营项目失败, {ssr_r}'
                    # call_r = pre_order_service.hood_callback(result)
                    # break
                    return result

                # 3.3 检查email（非必要）
                # email_check_r = self.vz_client.loyalty()

                # 3.4 提交乘客信息 和 辅营信息
                # 有重复键，需要用列表方式
                contact_info = mock_contact()
                # loyalty_rs = self.vz_client.loyalty(email=contact_info['email'])
                # logger.debug(f'loyalty_rs: {loyalty_rs}')
                passenger_count = hood_flight['fare_types'][self.tag_cabin_level]['availability']
                if passenger_count > 9:
                    passenger_count = 9
                passengers = mock_passengers(contact=contact_info, adult=passenger_count)
                booking_form = vz_book_service.build_booking_form(
                    booking_code=booking_code, contact=contact_info, passengers=passengers, ssr_r=ssr_r
                )

                submit_r = self.vz_client.quotation(booking_form=booking_form)
                if not submit_r or 'he quotation has been processed successfully' not in submit_r.get('message', ''):
                    result['error']['message'] = f'提交订单失败, submit_r {submit_r}'
                    # call_r = pre_order_service.hood_callback(result)
                    return result
                # else:
                #     # todo test
                #     logger.debug(submit_r)
                #     break

                # 4. 确认订单
                checkout_r = self.vz_client.booking_checkout(booking_code=booking_code)
                if not checkout_r:
                    result['error']['message'] = f'确认订单失败, checkout_r {checkout_r}'
                    # call_r = pre_order_service.hood_callback(result)
                    return result

                # 4.1 booking 状态检查
                book_status_r = self.vz_client.check_booking_status(booking_code)
                if book_status_r.get('error') or book_status_r.get('message', '') != 'Successfully':
                    result['error']['message'] = f'获取临时订单状态失败,book_status_r {book_status_r}'
                    # call_r = pre_order_service.hood_callback(result)
                    return result

                # 4.2 确认订单
                do_checkout_r = self.vz_client.booking_do_checkout(booking_code=booking_code)
                if not do_checkout_r:
                    result['error']['message'] = f'确认订单失败,do_checkout_r {do_checkout_r}'
                    # call_r = pre_order_service.hood_callback(result)
                    return result

                # 5. 支付选项
                payment_r = self.vz_client.payment(booking_code=booking_code)
                if not payment_r:
                    result['error']['message'] = f'获取支付选项失败,payment_r {payment_r}'
                    # call_r = pre_order_service.hood_callback(result)
                    return result

                # 5.1 支付税费检查
                payment_info = get_pay_vars(payment_r)
                function_cfg = payment_info.get('_FUNCOIN_CONFIG')
                qr_method = get_pay_method(payment_info, method_name=self.pay_method)
                if not payment_r or not payment_info or not function_cfg or not qr_method:
                    result['error']['message'] = f'支付税费检查失败, {payment_r}'
                    # call_r = pre_order_service.hood_callback(result)
                    return result

                payment_fee_check_r = self.vz_client.check_payment_fee(
                    booking_code=booking_code,
                    group=qr_method['group'],
                    fee=0,
                    key=qr_method['key'],
                    identifier=qr_method['identifier'],
                    code=qr_method['code'],
                    gateway=qr_method['gateway'],
                    funcoin_limit=function_cfg['funcoin_limit'],
                )
                if not payment_fee_check_r:
                    result['error']['message'] = f'支付税费检查失败,payment_fee_check_r {payment_fee_check_r}'
                    # call_r = pre_order_service.hood_callback(result)
                    return result

                # 5.2 选择支付方式（锁单）
                do_payment_r = self.vz_client.do_payment(
                    booking_code=booking_code,
                    group=qr_method['group'],
                    minimum=str(function_cfg['payment_minimum']),
                    exchange_rate=str(function_cfg['exchange_rate']),
                )
                if self.pay_method == 'QR Payment':
                    if not do_payment_r or not do_payment_r.get('payment_token'):
                        result['error']['message'] = '确认支付失败'
                        # call_r = pre_order_service.hood_callback(result)
                        return result
                elif self.pay_method == 'Global Card':
                    if not do_payment_r or not do_payment_r.get('url'):
                        result['error']['message'] = '确认支付失败'
                        # call_r = pre_order_service.hood_callback(result)
                        return result
                    else:
                        do_payment_r2 = self.vj_client._request('GET', do_payment_r['url'], allow_redirects=True)

                fare_info = hood_flight['fare_types'][self.tag_cabin_level]

                flights = vz_search_service.parse_flight(search_r, adult_count, child_count, infant_count)
                hood_flight_no = (flight['airline_code'] + flight['flight_number']).upper()
                single_flight = [f for f in flights['results'] if f['trips'][0]['flight_nos'][0] == hood_flight_no]
                logger.debug(f'航班搜索结果: {single_flight}')
                hood_result = {
                    "flight_info": single_flight[0],
                    "hold_success": True,
                    "seat_num": passenger_count,
                    "cabin_code": fare_info["code"],
                    "cabin_level": fare_info["code"],
                    "adult_base": single_flight[0]['trips'][0]['fares']['adult']['base'],
                    "adult_tax": single_flight[0]['trips'][0]['fares']['adult']['tax'],
                    "currency_code": fare_info['currency_code'],
                    "dep_airport_code": flight['departure_code'],
                    "arr_airport_code": flight['arrival_code'],
                    "dep_date": flight["departure_date_html"].split(' ')[0],
                    "flight_no": single_flight[0]['trips'][0]['flight_nos'][0],
                    "expired_time": real_expired_time,
                    "max_adult_base": 0,
                    "max_adult_tax": 0,
                    "extra": do_payment_r,
                }
                result['data'] = hood_result
                result['error']['code'] = ApiCodes.SUCCESS.value
                result['error']['message'] = ApiCodes.SUCCESS.label

                
                return result

        except Exception as e:
            logger.exception(e)
            # 只要有一次压位成功，就不返回失败
            # 所以这里只记录最后的失败信息，但是不重置code
            result['error']['message'] = str(e)
            # call_r = pre_order_service.hood_callback(result)
        finally:
            pass
        return result


def get_csrf_token(html_content: str):
    # Parse the HTML with BeautifulSoup
    soup = BeautifulSoup(html_content, 'html.parser')

    # Find the meta tag with name="csrf-token"
    csrf_meta = soup.find('meta', attrs={'name': 'csrf-token'})
    # logger.debug(csrf_meta)
    # logger.debug(type(csrf_meta))
    # logger.debug(csrf_meta.attrs)
    # logger.debug(csrf_meta.attrs.get('content', None))
    # Extract the content attribute value
    csrf_token = csrf_meta.attrs.get('content', None)

    logger.debug(f"CSRF Token: {csrf_token}")
    return csrf_token


def find_hood_flight(search_r: dict, tag_cabin_level: str, min_quantity: int, max_price: float, exclude_flights: list = []):
    if not search_r or not search_r.get('data') or not search_r['data'].get('departure_list'):
        return None

    for t_key, trip in search_r["data"]["departure_list"].items():
        if not trip.get('can_book'):
            logger.debug(f'无法预订，航班搜索返回数据: {trip}')
            continue

        flights = trip['flights']
        if len(flights) > 1:
            logger.debug(f'跳过联程、中转: {flights}')
            continue

        airline_code = flights[0]["airline_code"]
        flight_number = flights[0]["flight_number"]
        flight_no = airline_code + flight_number
        if flight_no in exclude_flights:
            logger.debug(f'跳过航班: {flight_no} (在排除列表中 {exclude_flights})')
            continue

        tag_fare = trip['fare_types'][tag_cabin_level]
        if tag_fare is None:
            logger.debug(f'跳过航班: {flight_no} (无{tag_cabin_level}舱)')
            continue

        if tag_fare['availability'] == 0 or tag_fare['availability'] > min_quantity:
            logger.debug(f'跳过航班: {flight_no} (余票数量 {tag_fare["availability"]}，无法压位)')
            continue

        if max_price > 0 and tag_fare["price_detail"]['fare']['adult']['baseAmount'] > max_price:
            logger.debug(f'跳过航班: {flight_no} (价格 {tag_fare["totalFare"]}，超过最大价格 {max_price})')
            continue

        # 复制待压位航班
        hood_flight = copy.deepcopy(trip)
        for fare_code in list(hood_flight['fare_types'].keys()):
            # 清理html描述，为占座做准备
            if 'description' in hood_flight['fare_types'][fare_code]:
                del hood_flight['fare_types'][fare_code]['description']
            if 'content' in hood_flight['fare_types'][fare_code]:
                del hood_flight['fare_types'][fare_code]['content']
        logger.info(
            f'找到可压位航班: {flight_no} {tag_fare["bookingKey"]} {tag_fare["totalFare"]} {tag_fare["availability"]}'
        )
        return hood_flight
    return None


def find_next_fare(search_r: dict, tag_flight_no: str):
    if not search_r or not search_r.get('data') or not search_r['data'].get('departure_list'):
        return None

    for t_key, trip in search_r["data"]["departure_list"].items():
        if not trip.get('can_book'):
            logger.debug(f'无法预订，航班搜索返回数据: {trip}')
            continue

        flights = trip['flights']
        if len(flights) > 1:
            logger.debug(f'跳过联程、中转: {flights}')
            continue

        airline_code = flights[0]["airline_code"]
        flight_number = flights[0]["flight_number"]
        flight_no = airline_code + flight_number
        if flight_no != tag_flight_no:
            logger.debug(f'跳过航班: {flight_no} (!={tag_flight_no})')
            continue

        for fare_code, fare_info in trip['fare_types'].items():
            if fare_info['availability'] > 0:
                return fare_info

    return None


random_last_name = lambda: random.choice(['ming', 'li', 'juan', 'xin', 'feng', 'wei', 'guo'])
random_first_name = lambda: random.choice(
    ['zhao', 'qian', 'sun', 'li', 'zhou', 'wu', 'zheng', 'wang', 'chen', 'jiang', 'shen', 'han', 'yang']
)
random_birthday = lambda min_age=13, max_age=65: datetime(
    year=datetime.now().year - random.randint(min_age, max_age), month=random.randint(1, 12), day=random.randint(1, 28)
).strftime('%Y-%m-%d')
random_sex = lambda: random.choice(['male', 'female'])
random_passport = lambda: 'E' + ''.join(random.sample("*********", 8))
random_card_valid_date = lambda: datetime(
    year=datetime.now().year + random.randint(2, 5), month=random.randint(1, 12), day=random.randint(1, 28)
).strftime('%Y-%m-%d')


def mock_contact():
    mobile = f'1371{"".join(random.sample("*********", 4))}{"".join(random.sample("*********", 3))}'
    first_name = random_first_name()
    contact = {
        "last_name": random_last_name(),
        "first_name": first_name,
        "mobile": mobile,
        "email": mobile
        + random.choice(
            [
                '@qq.com',
                '@sina.com',
                '@sohu.com',
                '@gmail.com',
                # f'@{"".join(random.sample("abcdefghijklmnopqrstuvwxyz", 6))}.com'
            ]
        ),
    }
    return contact


def mock_passenger(passenger_type: str = 'adult'):
    if passenger_type == 'child':
        passenger = {
            "last_name": random_last_name().lower(),
            "first_name": random_first_name().lower(),
            "birthday": random_birthday(min_age=2, max_age=11),
            "sex": random_sex(),
            "passenger_type": passenger_type,
            "country": "CN",
            "card_no": random_passport(),
            "card_valid_date": random_card_valid_date(),
            "card_country": "CN",
        }
    elif passenger_type == 'infant':
        passenger = {
            "last_name": random_last_name(),
            "first_name": random_first_name(),
            "birthday": random_birthday(min_age=0, max_age=1),
            "sex": random_sex(),
            "passenger_type": passenger_type,
            "country": "CN",
            "card_no": random_passport(),
            "card_valid_date": random_card_valid_date(),
            "card_country": "CN",
        }
    else:
        passenger = {
            "last_name": random_last_name(),
            "first_name": random_first_name(),
            "birthday": random_birthday(),
            "sex": random_sex(),
            "passenger_type": passenger_type,
            "country": "CN",
            "card_no": random_passport(),
            "card_valid_date": random_card_valid_date(),
            "card_country": "CN",
        }
    passenger['name'] = f'{passenger["last_name"]}/{passenger["first_name"]}'
    return passenger


def mock_passengers(contact: dict, adult: int = 1, child: int = 0, infant: int = 0):
    passengers = [
        {
            "name": f'{contact["last_name"]}/{contact["first_name"]}',
            "last_name": contact["last_name"],
            "first_name": contact["first_name"],
            "birthday": random_birthday(),
            "sex": random_sex(),
            "passenger_type": "adult",
            "country": "CN",
            "card_no": random_passport(),
            "card_valid_date": random_card_valid_date(),
            "card_country": "CN",
        }
    ]
    # children = []
    # infants = []
    while len(passengers) < adult:
        passengers.append(mock_passenger('adult'))
    for i in range(child):
        passengers.append(mock_passenger('child'))
    for i in range(infant):
        passengers.append(mock_passenger('infant'))

    return passengers


def extract_js_variable(html_content, variable_name):
    """
    从 HTML 中提取指定 JavaScript 变量的内容。

    参数:
    - html_content (str): 包含 HTML 内容的字符串。
    - variable_name (str): 要提取的 JavaScript 变量名。

    返回:
    - list: 包含所有找到的变量内容（解析后的 JSON 对象）。
    """
    # 创建 BeautifulSoup 对象
    soup = BeautifulSoup(html_content, 'html.parser')

    # 查找所有 <script> 节点
    script_tags = soup.find_all('script', {'type': 'text/javascript'})

    # 正则表达式匹配变量
    # pattern = rf'var\s+{re.escape(variable_name)}\s*=\s*({{.*?}});'
    pattern = rf"var[\s]?{re.escape(variable_name)}[\s]?=[\s]?({{.*?}});"

    # 用于存储找到的变量值
    variable_values = []

    # 遍历所有 <script> 节点
    for script_tag in script_tags:
        if script_tag.string:
            # 搜索变量
            if variable_name in script_tag.string:
                logger.debug(f"找到变量 {variable_name} 在脚本中 {script_tag.string}")
            match = re.search(pattern, script_tag.string, re.DOTALL)
            logger.debug(match)
            if match:
                json_str = match.group(1)
                try:
                    # 将 JSON 字符串解析为 Python 对象
                    variable_values.append(orjson.loads(json_str))
                except orjson.JSONDecodeError as e:
                    print(f"JSON 解码失败: {e}")

    return variable_values


def get_pay_vars(payment_r: str, var_names: tuple = ('_PAYMENT_METHODs', '_PAYMENT_GROUPs', '_FUNCOIN_CONFIG')):
    lines = payment_r.split('\n')
    for line in lines:
        logger.debug(line)
    pattern = r"\s+var\s?(.*?)\s?=[\s]?(.*?);\n"
    math = re.findall(pattern, payment_r)
    var_list = {}
    for name, value in math:
        if name in var_names:
            logger.debug(f"{name}: {value}")
            value = value.replace('\\\\', '\\').replace('\'', '"')
            var_list[name] = orjson.loads(value)

    return var_list


def get_pay_method(payment_info: dict, method_name: str = "QR Payment"):
    pay_groups = payment_info.get('_PAYMENT_GROUPs', [])
    for group in pay_groups:
        if method_name in group['methods']:
            payment_methods = payment_info.get('_PAYMENT_METHODs')
            if not payment_methods:
                return None
            for method in payment_methods:
                if method['name'] == method_name:
                    return {
                        'group': group['amelia_payment_identifier'],
                        'identifier': method['amelia_payment_identifier'],
                        'gateway': method['gateways'][0]['gateway'],
                        'code': method['gateways'][0]['code'],
                        'key': method['amelia_payment_config']['key'],
                    }
    return None
