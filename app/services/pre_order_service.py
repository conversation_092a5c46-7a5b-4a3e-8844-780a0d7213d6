import asyncio
import copy
from datetime import datetime, timedelta
from math import ceil
import random
import re
import aiohttp
from loguru import logger
import orjson
from app.sdks.flight_fare_sdk import FlightFareSdk
from app.config import settings
from app.sdks.flight_pre_order_sdk import FlightPreOrderSdk
from commons.consts.api_codes import ApiCodes
from commons.sdks import flight_pre_order
from commons.sdks.base import SdkClient

# todo
# 儿童价、婴儿价先按成人价计算

pre_order_sdk = SdkClient(host=settings.FLIGHT_PRE_ORDER_URL)


async def book_result_callback_async(task_data: dict, book_result: dict) -> bool:

    data = {
        "task_info": task_data,
        "data": book_result,
        "error": {"code": ApiCodes.SUCCESS.value, "message": ApiCodes.SUCCESS.label},
    }
    result = False
    po_sdk = FlightPreOrderSdk(host=settings.FLIGHT_PRE_ORDER_URL)
    resp = await po_sdk.book_callback(data=data, callback_url=task_data['callback_url'])
    logger.debug(f'压位占座回调 {task_data["callback_url"]} {data} {resp}')
    if resp.get('code', -1) == ApiCodes.SUCCESS.value:
        result = True
    return result


def hood_callback(result):

    task_info = result['task_info']

    hood_callback_request = flight_pre_order.hood_result_callback.HoodResultCallbackRequest(**result)
    if task_info.get('callback_url'):
        hood_callback_request._fix_url = task_info['callback_url']
    for _ in range(3):
        try:
            response = pre_order_sdk.send(hood_callback_request)
            logger.info(f'压位回调 {task_info["callback_url"]} request: {result} response: {response}')
            break
        except Exception as e:
            logger.warning(e)
    return response


def start_check(url: str, order_no: str) -> bool:
    result = False
    start_check_request = flight_pre_order.pay_start_check.PayStartRequest(order_no=order_no)

    start_check_request._fix_url = url
    try:
        start_check_result = pre_order_sdk.send(start_check_request)
        if start_check_result.get('code', -1) != ApiCodes.SUCCESS.value or not start_check_result.get('data'):
            logger.warning(f'订座前检查失败 {order_no} {start_check_result}')
        else:
            result = True
    except Exception as e:
        logger.warning(f'订座前检查失败 {order_no} {e}')
        result = False
    return result


def pay_result_callback(ticket_result: dict) -> bool:
    result = False
    try:
        callback_request = flight_pre_order.pay_result_callback.PayResultCallbackRequest(**ticket_result)
        callback_request._fix_url = ticket_result['task_info']['pay_callback_url']
        callback_result = pre_order_sdk.send(callback_request)
        if callback_result.get('code', -1) != ApiCodes.SUCCESS.value or not callback_result.get('data'):
            logger.warning(f'普通占座回调失败 {ticket_result} {callback_result}')
        else:
            result = True
    except Exception as e:
        logger.warning(f'普通占座回调失败 {ticket_result} {e}')
        result = False
    return result
