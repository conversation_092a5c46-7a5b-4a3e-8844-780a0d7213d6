# AI Rules for Unit Testing

## 📋 Core Principles

### 1. **Dependency Management**
- Import dependencies at the top of the file, not in individual test methods
- Only import what is actually used in the tests
- Use `from module import SpecificClass` instead of `import module`

### 2. **Mock Strategy**
- Only mock external APIs, databases, and network calls
- Do not over-mock internal business logic
- Mock at the appropriate level (method level, not class level when possible)

### 3. **Test Structure**
- Follow AAA pattern: Arrange, Act, Assert
- One assertion per test method when possible
- Clear test method names that describe the scenario

### 4. **Variable Usage**
- Use underscore `_` for unused variables: `_, kwargs = call_args`
- Avoid declaring variables that are not used
- Use descriptive variable names

## 🔧 Code Standards

### Import Pattern
```python
#!/usr/bin/env python3
"""
Module description
"""

from unittest.mock import patch, MagicMock

from app.module.target_class import TargetClass
```

### Test Class Pattern
```python
class TestTargetClass:
    """Test TargetClass functionality"""

    def test_method_success_scenario(self):
        """Test method_name - success scenario"""
        # Arrange
        instance = TargetClass()
        expected_result = 'expected'
        
        # Act
        with patch.object(instance, 'external_method') as mock_external:
            mock_external.return_value = 'mocked_response'
            result = instance.target_method()
        
        # Assert
        assert result == expected_result
        mock_external.assert_called_once()

    def test_method_error_scenario(self):
        """Test method_name - error scenario"""
        # Test error handling
        pass

    def test_method_boundary_scenario(self):
        """Test method_name - boundary scenario"""
        # Test edge cases
        pass
```

### Mock Usage Pattern
```python
# ✅ Good: Mock external dependencies
with patch('requests.get') as mock_get:
    mock_get.return_value.json.return_value = {'data': 'test'}

# ✅ Good: Mock database calls
with patch('app.services.db_service.get_data') as mock_db:
    mock_db.return_value = [{'id': 1, 'name': 'test'}]

# ❌ Bad: Over-mocking internal logic
with patch.object(service, '_internal_calculation') as mock_calc:
    # This should not be mocked unless it's an external call
```

### Variable Handling
```python
# ✅ Good: Use underscore for unused variables
_, kwargs = mock_method.call_args
form_data = kwargs['form_data']

# ❌ Bad: Declare unused variables
args, kwargs = mock_method.call_args  # args is unused
```

## 📊 Test Coverage Requirements

### 1. **Scenario Coverage**
- **Normal scenarios**: Happy path testing
- **Error scenarios**: Exception handling
- **Boundary scenarios**: Edge cases and limits

### 2. **Coverage Target**
- Aim for 95%+ code coverage
- Focus on business logic coverage
- Include integration points

### 3. **Test Types**
- **Unit tests**: Test individual methods/functions
- **Integration tests**: Test component interactions
- **Debug tests**: Place in `/tests/debug` directory and exclude from main test runs

## 🚫 Anti-Patterns to Avoid

### 1. **Import Anti-Patterns**
```python
# ❌ Bad: Importing in test methods
def test_method(self):
    from app.module import SomeClass  # Should be at top

# ❌ Bad: Unused imports
import pytest  # Not used in file
from unittest.mock import Mock, patch  # Mock not used
```

### 2. **Mock Anti-Patterns**
```python
# ❌ Bad: Mocking everything
with patch.object(service, 'method1'), \
     patch.object(service, 'method2'), \
     patch.object(service, 'method3'):
    # Over-mocking makes tests brittle

# ❌ Bad: Not mocking external calls
def test_api_call(self):
    result = service.call_external_api()  # Will make real HTTP call
```

### 3. **Assertion Anti-Patterns**
```python
# ❌ Bad: Multiple unrelated assertions
def test_complex_scenario(self):
    assert result.status == 'success'
    assert result.data['count'] > 0
    assert result.timestamp is not None
    # Should be split into separate tests

# ❌ Bad: No assertions
def test_method(self):
    service.method()  # No verification of behavior
```

## 🔄 Refactoring Guidelines

### 1. **When to Refactor Tests**
- When adding new functionality
- When test coverage drops below 95%
- When tests become brittle or hard to maintain
- When mock patterns become inconsistent

### 2. **How to Refactor**
- Extract common setup into helper methods
- Consolidate similar test patterns
- Remove redundant tests
- Update mock strategies to be less brittle

### 3. **Validation Steps**
- Run full test suite after refactoring
- Check coverage reports
- Verify no functionality is broken
- Ensure tests are still readable

## 📁 File Organization

### Directory Structure
```
tests/
├── unit/
│   ├── clients/
│   ├── services/
│   └── utils/
├── integration/
├── debug/  # Excluded from main test runs
└── fixtures/
```

### Naming Conventions
- Test files: `test_module_name.py`
- Test classes: `TestClassName`
- Test methods: `test_method_scenario`

## 🎯 Quality Checklist

Before submitting tests, verify:
- [ ] No unused imports
- [ ] No unused variables (use `_` for intentionally unused)
- [ ] External dependencies are mocked
- [ ] Tests follow AAA pattern
- [ ] Clear test method names
- [ ] Appropriate assertions
- [ ] Error scenarios covered
- [ ] Boundary cases tested
- [ ] Coverage target met (95%+)

## 🔗 Integration with Existing Codebase

### 1. **Check Existing Tests First**
- Review similar test files for patterns
- Follow established conventions
- Reuse existing test utilities

### 2. **Adapt to Project Structure**
- Use project-specific mock patterns
- Follow project naming conventions
- Integrate with existing CI/CD pipeline

### 3. **Maintain Consistency**
- Use same testing frameworks
- Follow same assertion styles
- Maintain same coverage standards
