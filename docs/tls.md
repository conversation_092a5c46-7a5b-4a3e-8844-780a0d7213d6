# tls 接口实例

## 普通get，返回text

```json
{
  "tls_type": "curl_cffi",
  "method": "GET",
  "url": "https://www.sina.com",
  "data": "",
  "proxy_str": "***************************************************"
}
```

## get，返回json

```json
{
    "tls_type": "curl_cffi",
    "method": "GET",
    "url": "https://th.vietjetair.com/flight/getFlights?tripType=onewaytrip&from_where=BKK&to_where=HKT&start=09%2F04%2F2025&end=09%2F04%2F2025&adultCount=1&childCount=0&infantCount=0&promoCode=&currency=thb",
    "headers": {
        "accept": "application/json, text/javascript, */*; q=0.01",
        "accept-language": "zh-CN,zh;q=0.9",
        "cache-control": "no-cache",
        "dnt": "1",
        "pragma": "no-cache",
        "priority": "u=1, i",
        "referer": "https://th.vietjetair.com/flight?tripType=onewaytrip&currency=thb&from_where=BKK&start=09%2F04%2F2025&to_where=HKT&end=09%2F04%2F2025&adultCount=1&childCount=0&infantCount=0&promoCode=&findLowestFare=",
        "sec-ch-ua": "\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"macOS\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-csrf-token": "B8ctocJKzouoOY5PS35efoxEMhjDKQdY05EOWVlp",
        "x-requested-with": "XMLHttpRequest"
    },
    "cookies": {
        "booking_cookie_agreement_enable": "eyJpdiI6IkxvaWREQXZVZGM4T3ZOM0J5ZCt3V2c9PSIsInZhbHVlIjoiZVNYejVSOHljNFRMN3MxSFFiT1A5ZDBjbzNVVjRXdjd0WUgxUlQ4ZGFFSjd6azQ2RWNjbW5leWNqeTlFYjd3bzRyUXY2SG5RUUxNOHZkTUJJRmRRS3BwY0FzZ0ZQSTlqb2tBRjh4YkVLWmQrdER3b25TTkpIWGIxTlg0VUpjbDJNWnFCZURRNHM3TFJqamg3RnE5aWdBPT0iLCJtYWMiOiJkZDJkNjRlMDI1YTNjMzI5YmMzMDk0NzFiZmQxNmU1Y2JmNTgxZDZiNGMzN2YwYjNhYTJkNjRlNDliZjY3YWEyIn0%3D",
        "_ga": "GA1.1.1786123807.1743501293",
        "_gcl_au": "1.1.47216789.1743506403",
        "_tt_enable_cookie": "1",
        "_ttp": "01JQRH8Z4YQ7B0CDS8BQC035RY_.tt.1",
        "_fbp": "fb.1.1743506406929.172200648317843589",
        "_atrk_siteuid": "AwI7hrSup_8-YHDo",
        "appier_random_unique_id_ViewLanding_VietJetAir": "YDBCEgTZ9IMVnOKbA1LeUQ",
        "appier_utmz": "%7B%22csr%22%3A%22www.vietjetair.com%22%2C%22timestamp%22%3A1743506412%2C%22lcsr%22%3A%22www.vietjetair.com%22%7D",
        "_hjSessionUser_3486574": "eyJpZCI6ImQyOGYxNDM2LWU1ZTEtNTJhYS05NzVlLTc1ODZhODMwMWU1MCIsImNyZWF0ZWQiOjE3NDM1MDY0MTMyNzAsImV4aXN0aW5nIjp0cnVlfQ==",
        "appier_random_unique_id_Click_LetsGo": "jshYT5msk_NssklkEVIl1X",
        "cto_bundle": "adl8vV9iRHZrYkc3UldQcVlxQzFuZHFHc21QNnlrQk8lMkJiVEhiRGY5OVp2ZEVuVlFVbE5BQ0o0RVUwTFNWdTFjVGNaeiUyQlM3dXBBYkFEaUFDMThkRXpSaHVIWDhxUGlYWWFDNXg3emU0R2NMSjR1eUpwUzk1cnVjaEFHJTJCY1o4ZjZ1Nm9HNVlkOFo5RGduYjBHU1F1alNrRWt6RkFvUnJIZWo5S2szNE1hMGI1dEppRTQlM0Q",
        "_ga_KML2W7BV5C": "GS1.1.1743997894.2.1.1743998092.46.0.0",
        "_ga_Z47TKGJW5K": "GS1.1.1743997963.5.1.1743998393.60.0.0",
        "_ga_6ZGCC82BTL": "GS1.1.1743998029.2.1.1743998401.0.0.*********",
        "_ga_1DQMSR0C48": "GS1.1.1744181497.6.1.1744181638.0.0.0",
        "AWSALBTG": "EPDBwsPt/XIkFA1W56AN8ByiRukFqfxM3j9t/sO53Fuxzuhc6s0d5AKsuCN1ScDebjJ+S++lhDUJYq0j3IqkdlBOgBIVxHiRxLPrbhD7LTGSoNGFv4+bM2+SkeuYsJ/uUy6udYyu/dmMSxjmgqIPYtLz9stU8iNWHltP2HbgRJ3h",
        "AWSALBTGCORS": "EPDBwsPt/XIkFA1W56AN8ByiRukFqfxM3j9t/sO53Fuxzuhc6s0d5AKsuCN1ScDebjJ+S++lhDUJYq0j3IqkdlBOgBIVxHiRxLPrbhD7LTGSoNGFv4+bM2+SkeuYsJ/uUy6udYyu/dmMSxjmgqIPYtLz9stU8iNWHltP2HbgRJ3h",
        "XSRF-TOKEN": "eyJpdiI6ImpicEV6VlRwblhiWGtzbDFRM1N1cEE9PSIsInZhbHVlIjoibDJhb0hJZ1R4UGR2MENHVnpiWktwbzZLVTZvWDdFbUpXbUhIMVFBYU14YVl6TWY2OTN3WFk1dVZySU1EL2F5aGQxR1JycG43T1YybnVESjJMcDlmT2RZWGxlbENFVHAwcXdBeEt2UW5kdjZIeTZ6TDJQWXZrVzV4NTZpQ1ViREEiLCJtYWMiOiI3Y2ZhZjE1YjhiYTA5MzI0MTQ2MzkxNTM3MmQxYjliZTQ0MDViZDY3M2UzMzllNmIzZGJlZGYyODJkZTVhMWU1In0%3D",
        "thaivietjet_session": "eyJpdiI6IldoazZJSVRRUWxaTk9IZG5NSFN5UkE9PSIsInZhbHVlIjoiT2tMaHl1VWNtNC9jZjh5Y2FYY2t5a3JTc1Q3MVNCZXFicHpnV1dkVGVFTHdwRW9TRTcwRXozWkdzajNmUlkzUE5BZkZ5ZUJENy9RYndMaWMxdk5FcHVSeHdaR1I3M1hvLzVvYVREOXBhMEZ6UWpFdkdwSHpyZ2VjWjFiSTFhVGMiLCJtYWMiOiJjOGYzYjk4Mzk1YTU2MmQ4NjA2YTk2ZjdiNmI4YTNhY2VhZDBjNDE3MDZkMDhiMWE2OWE2NDllZjY1MTJkNjE5In0%3D",
        "flight_filter": "?tripType=onewaytrip&currency=thb&from_where=BKK&start=09%2F04%2F2025&to_where=HKT&end=09%2F04%2F2025&adultCount=1&childCount=0&infantCount=0&promoCode=&findLowestFare="
    },
    "proxy_str": "***************************************************"
}
```

## post，返回json（通过headers.content-type判断是form提交还是json提交）

```
{
    "tls_type": "curl_cffi",
    "method": "GET",
    "url": "https://th.vietjetair.com/flight/getFlights?tripType=onewaytrip&from_where=BKK&to_where=HKT&start=09%2F04%2F2025&end=09%2F04%2F2025&adultCount=1&childCount=0&infantCount=0&promoCode=&currency=thb",
    "headers": {
        "accept": "*/*",
        "accept-language": "zh-CN,zh;q=0.9",
        "cache-control": "no-cache",
        "content-type": "application/json",
        "dnt": "1",
        "origin": "https://main.eastarjet.com",
        "pragma": "no-cache",
        "priority": "u=1, i",
        "referer": "https://main.eastarjet.com/",
        "sec-ch-ua": "\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"macOS\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site",
        "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-wtap-msp": "EASTARJET",
        "x-wtap-po": "eastarjet",
        "x-wtap-sp1": "https://main.eastarjet.com/schedule"
    },
    "cookies": {
        "__cf_bm": "HJMAyTkMPB7ws59Tbtyy6veoKs9B..BJe5P7gUTXBgo-1744182254-1.0.1.1-1MaAf3rZP58Td17OW23zflgb03QN3EXrLSprp5cuFidAoAtdWFG9R7kfr0K6ECDG8gBLGC5bgHlR73Jd7FUPgsJBMVdF.JtC9wW1nRRBlRA",
        "_cfuvid": "rCGOqax3p1Nvw0h_HuhvsuphJXudUdjLGMdtgKS_5Ig-1744182254118-0.0.1.1-604800000",
        "_ga": "GA1.1.893255597.1744182257",
        "JSESSIONID": "97067CAA00DD9A5B9DDDDCE52D70D22F",
        "ch-veil-id": "23cd7947-58ff-4f02-9358-755abd870725",
        "_fbp": "fb.1.1744182262361.769514989282935650",
        "selected_country_code": "CN",
        "selected_culture_code": "zh_CN",
        "ch-session-179382": "eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzZXMiLCJrZXkiOiIxNzkzODItNjdmNjFiZjRjMjFiN2EwNzg2MTkiLCJpYXQiOjE3NDQxODIzMDcsImV4cCI6MTc0Njc3NDMwN30.rJs6ye0TZCxSOo5DXejJR8w3G_t4MjSOvkZSVUJp0nI",
        "_ga_3MJCE20ZX8": "GS1.1.1744182257.1.1.1744182347.38.0.1947188798"
    },
    "data": {
        "journeys": [
            {
                "departure": [
                    "PUS"
                ],
                "origin": [
                    "SEL"
                ],
                "departureDate": "2025-04-19"
            }
        ],
        "passengers": [
            {
                "type": "ADT",
                "count": 1
            }
        ],
        "promotionCode": "",
        "currency": "KRW"
    },
    "proxy_str": "***************************************************"
}
```