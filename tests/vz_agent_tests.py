from datetime import datetime, timed<PERSON>ta
import os
from loguru import logger
import or<PERSON><PERSON>
import pytest
from app.config import settings

from app.services import vz_agent_helper, vz_agent_service, vz_hood_service
from app.services.vz_agent_service import VZAgentService


@pytest.fixture(scope='session')
def agent_account():
    account = vz_agent_service.get_account()
    return account


def test_login(agent_account):
    # 读取环境变量
    username = agent_account['username']
    password = agent_account['password']
    assert username
    assert password

    sv = VZAgentService(username=username, password=password)
    sv.set_proxy()
    login_result = sv.login()
    sv.save_cookies()

    assert login_result.get('accessToken')


def test_search(agent_account):
    sv = VZAgentService(username=agent_account['username'], password=agent_account['password'])
    # sv.set_proxy()

    assert sv.load_cookies()
    sv.search(dep='BKK', arr='CEI', date='2025-02-28', adult=1, child=0, infant=0)


def test_parse_flight():
    html = ''
    with open('tests/search_result.html', 'r', encoding='utf-8') as f:
        html = f.read()
    result = vz_agent_helper.parse_flight(html, date='28/02/2025', passenger_num=1)
    assert result


def test_book(agent_account):
    sv = VZAgentService(username=agent_account['username'], password=agent_account['password'])
    # sv.client.pay_test()
    # sv.client.session.verify = False
    # # sv.set_proxy()
    # sv.client.session.proxies = {'http': 'http://192.168.1.101:8888', 'https': 'http://192.168.1.101:8888'}

    # # assert sv.load_cookies()
    sv.login()
    # sv.client.session.cookies.clear()
    # sv.client.session.cookies.update({'ASP.NET_SessionId': '1jl2h0wm1qgxr3bhogz10osm'})
    # sv.client.agent_options()
    # sv.client.pay_test()
    dep_date = '2025-04-25'
    adult = 1
    child = 0
    # 注意下面添加联系人时，数量和类型要与查询时一致
    search_rs = sv.search(dep='BKK', arr='CEI', date=dep_date, adult=adult, child=child)
    assert search_rs
    fare_key = search_rs['results'][0]['extra']['fare_key']
    # fare_key = '11,Z1_ECO,O'
    # contact_info = vz_hood_service.mock_contact()
    # passengers = vz_hood_service.mock_passengers(contact=contact_info, adult=adult, child=child)
    passengers = [
        {
            "name": "JIANG/FENGLING",
            "last_name": "JIANG",
            "first_name": "FENGLING",
            "birthday": "1991-10-05",
            "sex": "female",
            "passenger_type": "adult",
            "country": "RU",
            "card_no": "",
            "card_valid_date": "2025-02-21",
            "card_country": "RU",
        },
        # {
        #     "name": "JIANG/FENGLING",
        #     "last_name": "JIANG",
        #     "first_name": "FENGLING",
        #     "birthday": "1991-10-05",
        #     "sex": "female",
        #     "passenger_type": "adult",
        #     "country": "CN",
        #     "card_no": "E69139880",
        #     "card_valid_date": "2026-02-24",
        #     "card_country": "CN",
        # },
    ]
    sv.book(fare_key=fare_key, passengers=passengers)


def test_parse_book_result():
    html = ''
    with open('tests/book_result.html', 'r', encoding='utf-8') as f:
        html = f.read()
    result = vz_agent_helper.parse_book_result(html)
    assert result


def test_scan_book():
    params = {
        "callback_url": "http://192.168.1.158:8080/api/v1/flight_fare/crawler/callback/verify/book/result",
        "order_no": "20250225130210807",
        "mock_pnr": "JG1MWS",
        "airline_code": "VZ",
        "dep_airport_code": "BKK",
        "arr_airport_code": "UBP",
        "dep_date": "2025-02-27",
        "flight_no": "VZ220",
        "adult": 1,
        "child": 0,
        "infant": 0,
        "currency_code": "THB",
        "src_adult_base": 1040,
        "src_adult_tax": 433.97,
        "passengers": [
            {
                "name": "HUANG/TAO",
                "last_name": "HUANG",
                "first_name": "TAO",
                "birthday": "1998-08-14",
                "sex": "male",
                "passenger_type": "adult",
                "country": "CN",
                "card_no": "PE2205799",
                "card_valid_date": "2027-08-05",
                "card_country": "CN",
            }
        ],
        "unique_id": "296542c6-f934-4d2e-b725-c024e50eb5f2",
    }
    reasult = vz_agent_service.run_scan_book(params)
    assert reasult


def test_hood_book():
    params = {
        "callback_url": "http://192.168.1.158:8082/api/v1/flight_pre_order/crawler/callback/pay/result",
        "order_no": "20250307132950688",
        "mock_pnr": "5DIEAR",
        "airline_code": "VZ",
        "dep_airport_code": "BKK",
        "arr_airport_code": "CAN",
        "dep_date": "2025-04-05",
        "flight_no": "VZ3720",
        "adult": 1,
        "child": 0,
        "infant": 0,
        "currency_code": "THB",
        "src_adult_base": 10000.0,
        "src_adult_tax": 2143.1,
        "stop_loss": {"total_price": 10000.0, "currency_code": "THB", "rate": 0.55},
        "passengers": [
            {
                "name": "yao/feng",
                "last_name": "yao",
                "first_name": "feng",
                "birthday": "2000-01-01",
                "sex": "male",
                "passenger_type": "adult",
                "country": "CN",
                "card_no": "E8796543",
                "card_valid_date": "2028-12-31",
                "card_country": "CN",
            }
        ],
        "contact": {
            "name": "yao/feng",
            "last_name": "yao",
            "first_name": "feng",
            "mobile": "13800138000",
            "email": "<EMAIL>",
            "country": "CN",
            "area_code": "+86",
        },
        "unique_id": "cb3bde2a-c99a-4e62-b2f6-b95c3a6f9852",
        "keep_minutes": 6,
        "host": "agents.vietjetair.com",
        "proxy": "",
    }
    result = vz_agent_service.run_hood_book(params)
    logger.debug(result)
    assert result
