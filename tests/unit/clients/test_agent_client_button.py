#!/usr/bin/env python3
"""
Agent Client Button参数单元测试
测试agent_client中所有方法的button参数功能
"""

from unittest.mock import patch

from app.clients.agent_client import VZAgentClient


class TestAgentClientButton:
    """测试AgentClient中所有方法的button参数"""

    def test_travel_options_button_default(self):
        """测试travel_options方法的默认button参数"""
        # 创建客户端实例
        client = VZAgentClient()
        client._view_state = 'test_view_state'
        client._view_state_generator = 'test_generator'
        client._debug_id = 'test_debug_id'

        # Mock switch_request方法
        with patch.object(client, 'switch_request') as mock_switch_request:
            mock_switch_request.return_value = 'mocked_response'

            # 测试默认button参数（continue）
            client.travel_options(fare_key='test_fare_key')

            # 验证调用参数
            args, kwargs = mock_switch_request.call_args
            form_data = kwargs['form_data']

            # 检查button参数
            form_dict = dict(form_data)
            assert 'button' in form_dict
            assert form_dict['button'] == 'continue'

    def test_travel_options_button_back(self):
        """测试travel_options方法的back button参数"""
        client = VZAgentClient()
        client._view_state = 'test_view_state'
        client._view_state_generator = 'test_generator'
        client._debug_id = 'test_debug_id'

        with patch.object(client, 'switch_request') as mock_switch_request:
            mock_switch_request.return_value = 'mocked_response'

            # 测试自定义button参数（back）
            client.travel_options(fare_key='test_fare_key', button='back')

            # 验证调用参数
            _, kwargs = mock_switch_request.call_args
            form_data = kwargs['form_data']

            # 检查button参数
            form_dict = dict(form_data)
            assert form_dict['button'] == 'back'

    def test_details_button_default(self):
        """测试details方法的默认button参数"""
        client = VZAgentClient()
        client._view_state = 'test_view_state'
        client._view_state_generator = 'test_generator'
        client._debug_id = 'test_debug_id'
        client._lst_company_list = 'test_company'

        passengers = [{'passenger_type': 'adult', 'first_name': 'John', 'last_name': 'Doe'}]

        with patch.object(client, 'switch_request') as mock_switch_request:
            with patch('app.services.agent_helper.get_passengers_form_data') as mock_get_passengers:
                mock_switch_request.return_value = 'mocked_response'
                mock_get_passengers.return_value = {}

                # 测试默认button参数
                client.details(passengers=passengers)

                # 验证调用参数
                _, kwargs = mock_switch_request.call_args
                form_data = kwargs['form_data']

                assert form_data['button'] == 'continue'

    def test_details_button_custom(self):
        """测试details方法的自定义button参数"""
        from app.clients.agent_client import VZAgentClient

        client = VZAgentClient()
        client._view_state = 'test_view_state'
        client._view_state_generator = 'test_generator'
        client._debug_id = 'test_debug_id'
        client._lst_company_list = 'test_company'

        passengers = [{'passenger_type': 'adult', 'first_name': 'John', 'last_name': 'Doe'}]

        with patch.object(client, 'switch_request') as mock_switch_request:
            with patch('app.services.agent_helper.get_passengers_form_data') as mock_get_passengers:
                mock_switch_request.return_value = 'mocked_response'
                mock_get_passengers.return_value = {}

                # 测试自定义button参数
                client.details(passengers=passengers, button='back')

                # 验证调用参数
                args, kwargs = mock_switch_request.call_args
                form_data = kwargs['form_data']

                assert form_data['button'] == 'back'

    def test_confirm_button_default(self):
        """测试confirm方法的默认button参数"""
        from app.clients.agent_client import VZAgentClient

        client = VZAgentClient()
        client._view_state = 'test_view_state'
        client._view_state_generator = 'test_generator'

        passengers = [{'passenger_type': 'adult', 'first_name': 'John', 'last_name': 'Doe'}]

        with patch.object(client, 'switch_request') as mock_switch_request:
            with patch('app.services.agent_helper.get_confirm_form_data') as mock_get_confirm:
                mock_switch_request.return_value = 'mocked_response'
                mock_get_confirm.return_value = {}

                # 测试默认button参数
                client.confirm(passengers=passengers)

                # 验证调用参数
                args, kwargs = mock_switch_request.call_args
                form_data = kwargs['form_data']

                assert form_data['button'] == 'continue'

    def test_confirm_button_custom(self):
        """测试confirm方法的自定义button参数"""
        from app.clients.agent_client import VZAgentClient

        client = VZAgentClient()
        client._view_state = 'test_view_state'
        client._view_state_generator = 'test_generator'

        passengers = [{'passenger_type': 'adult', 'first_name': 'John', 'last_name': 'Doe'}]

        with patch.object(client, 'switch_request') as mock_switch_request:
            with patch('app.services.agent_helper.get_confirm_form_data') as mock_get_confirm:
                mock_switch_request.return_value = 'mocked_response'
                mock_get_confirm.return_value = {}

                # 测试自定义button参数
                client.confirm(passengers=passengers, button='back')

                # 验证调用参数
                args, kwargs = mock_switch_request.call_args
                form_data = kwargs['form_data']

                assert form_data['button'] == 'back'

    def test_edit_res_button_default(self):
        """测试edit_res方法的默认button参数"""
        from app.clients.agent_client import VZAgentClient

        client = VZAgentClient()
        client._view_state = 'test_view_state'
        client._view_state_generator = 'test_generator'

        form_data = {'test_field': 'test_value'}

        with patch.object(client, 'switch_request') as mock_switch_request:
            mock_switch_request.return_value = 'mocked_response'

            # 测试默认button参数
            client.edit_res(form_data=form_data)

            # 验证调用参数
            args, kwargs = mock_switch_request.call_args
            updated_form_data = kwargs['form_data']

            assert updated_form_data['button'] == 'addpayment'

    def test_edit_res_button_custom(self):
        """测试edit_res方法的自定义button参数"""
        from app.clients.agent_client import VZAgentClient

        client = VZAgentClient()
        client._view_state = 'test_view_state'
        client._view_state_generator = 'test_generator'

        form_data = {'test_field': 'test_value'}

        with patch.object(client, 'switch_request') as mock_switch_request:
            mock_switch_request.return_value = 'mocked_response'

            # 测试自定义button参数
            client.edit_res(form_data=form_data, button='cancel')

            # 验证调用参数
            args, kwargs = mock_switch_request.call_args
            updated_form_data = kwargs['form_data']

            assert updated_form_data['button'] == 'cancel'

    def test_search_res_button_search(self):
        """测试search_res方法的search button参数"""
        from app.clients.agent_client import VZAgentClient

        client = VZAgentClient()
        client._view_state = 'test_view_state'
        client._view_state_generator = 'test_generator'

        with patch.object(client, 'switch_request') as mock_switch_request:
            mock_switch_request.return_value = 'mocked_response'

            # 测试search button参数
            client.search_res(real_pnr='TEST123', button='search')

            # 验证调用参数
            args, kwargs = mock_switch_request.call_args
            form_data = kwargs['form_data']

            assert form_data['button'] == 'search'

    def test_search_res_button_continue(self):
        """测试search_res方法的continue button参数"""
        from app.clients.agent_client import VZAgentClient

        client = VZAgentClient()
        client._view_state = 'test_view_state'
        client._view_state_generator = 'test_generator'

        with patch.object(client, 'switch_request') as mock_switch_request:
            mock_switch_request.return_value = 'mocked_response'

            # 测试continue button参数
            client.search_res(real_pnr='TEST123', button='continue')

            # 验证调用参数
            args, kwargs = mock_switch_request.call_args
            form_data = kwargs['form_data']

            assert form_data['button'] == 'continue'

    def test_agent_pwd_button_default(self):
        """测试agent_pwd方法的默认button参数"""
        from app.clients.agent_client import VZAgentClient

        client = VZAgentClient()
        client._view_state = 'test_view_state'
        client._view_state_generator = 'test_generator'

        with patch.object(client, 'switch_request') as mock_switch_request:
            mock_switch_request.return_value = 'mocked_response'

            # 测试默认button参数
            client.agent_pwd()

            # 验证调用参数
            args, kwargs = mock_switch_request.call_args
            form_data = kwargs['form_data']

            assert form_data['button'] == 'cancel'

    def test_agent_pwd_button_custom(self):
        """测试agent_pwd方法的自定义button参数"""
        from app.clients.agent_client import VZAgentClient

        client = VZAgentClient()
        client._view_state = 'test_view_state'
        client._view_state_generator = 'test_generator'

        with patch.object(client, 'switch_request') as mock_switch_request:
            mock_switch_request.return_value = 'mocked_response'

            # 测试自定义button参数
            client.agent_pwd(button='confirm')

            # 验证调用参数
            args, kwargs = mock_switch_request.call_args
            form_data = kwargs['form_data']

            assert form_data['button'] == 'confirm'
