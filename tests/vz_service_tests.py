from loguru import logger
import or<PERSON><PERSON>
import pytest

from app.services import vz_book_service, vz_hood_service


def test_hood_service():
    hood_service = vz_hood_service.VZHoodService()
    task_data = {
        "crawler_code": "vz",
        "callback_url": "http://*************:8082/api/v1/flight_pre_order/crawler/callback/hood/result",
        "po_rule_code": "E13481719",
        "dep_airport_code": "BKK",
        "arr_airport_code": "CEI",
        "dep_date": "2025-03-06",
        "flight_no": "VZ132",
        "min_quantity": 3,
    }
    # hood_service.vz_client.host = '**************'
    # hood_service.vz_client.host = '************'
    # hood_service.vz_client.host = '***********'
    # hood_service.vz_client.session.proxies = {'http': 'http://localhost:8888', 'https': 'http://localhost:8888'}
    hood_result = hood_service.run_hood(task_data=task_data)
    logger.debug(hood_result)


def test_find_hood_flight():
    search_r = None
    with open('tests/vz_search_result.json', 'r') as f:
        search_r = orjson.loads(f.read())

    hood_flight = vz_hood_service.find_hood_flight(search_r=search_r, exclude_flights='VZ200', tag_cabin_level='Eco')
    logger.debug(hood_flight)
    assert hood_flight


def test_build_booking_data():
    contact_info = vz_hood_service.mock_contact()
    ssr_r = None
    with open('tests/vz_ssr_result.json', 'r') as f:
        ssr_r = orjson.loads(f.read())
    passenger_count = 5
    passengers = vz_hood_service.mock_passengers(contact=contact_info, adult=passenger_count)
    book_data = vz_book_service.build_booking_form(
        booking_code="GEAWP4NTM", contact=contact_info, passengers=passengers, ssr_r=ssr_r
    )
    logger.debug(book_data)


def test_parse_payment():
    payment_r = None
    with open('tests/vz_payment.html', 'r') as f:
        payment_r = f.read()

    payment_info = vz_hood_service.get_pay_vars(payment_r)
    function_cfg = payment_info.get('_FUNCOIN_CONFIG')
    qr_method = vz_hood_service.get_pay_method(payment_info, 'QR Payment')
    logger.debug(payment_info)
    logger.debug(function_cfg)
    logger.debug(qr_method)
