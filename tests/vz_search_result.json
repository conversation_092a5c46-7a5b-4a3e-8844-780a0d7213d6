{"data": {"departure_list": {"QcCbieEcFuYhONTHBja6Tƒ8ƒntFPuWwwFnsTr1y4rq3PBrXnomyOrSQvWKgGvXcOPSR8pjfpKn30Wv0uXTlJVfgHO¥mgGj5mDv36IEpCbHnVKW5uyNGm2AE6OVM8jofQyQIIZVEI4iOYc63F5avdArGVLwevyuXcy1BDuF2RVI6EoyTztx4gYIqbN2XZRCVzY7CFnPov8uPqb8rJgDvT¥1NtLC¥bj5BgXzXRGoUrU7Q=0": {"index": 0, "id": "QcCbieEcFuYhONTHBja6Tƒ8ƒntFPuWwwFnsTr1y4rq3PBrXnomyOrSQvWKgGvXcOPSR8pjfpKn30Wv0uXTlJVfgHO¥mgGj5mDv36IEpCbHnVKW5uyNGm2AE6OVM8jofQyQIIZVEI4iOYc63F5avdArGVLwevyuXcy1BDuF2RVI6EoyTztx4gYIqbN2XZRCVzY7CFnPov8uPqb8rJgDvT¥1NtLC¥bj5BgXzXRGoUrU7Q=", "name": "BangKok - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to Phuket", "route_name": "BKK → HKT", "numberOfStops": 1, "flights": [{"airline_code": "VZ", "airline_name": "Thai VietJet Air", "flight_number": "308", "aircraft_model": "321H", "departure": "BangKok (Suvarnabhumi)", "departure_code": "BKK", "arrival": "Phuke<PERSON>", "arrival_code": "HKT", "trip_hour": "01h 40m", "departure_date": {"date": "2025-04-07 19:50:00.000000", "timezone_type": 3, "timezone": "Asia/Bangkok"}, "departure_date_html": "2025-04-07 19:50:00", "arrival_date": {"date": "2025-04-07 21:30:00.000000", "timezone_type": 3, "timezone": "Asia/Bangkok"}, "arrival_date_html": "2025-04-07 21:30:00", "departure_time": "Mon Apr 07 19:50 PM", "departure_time_html": "Mon, 07 Apr 2025", "departure_hour": "19:50", "departure_dm": "07 Apr", "departure_format_html": "07/04/2025", "arrival_time": "Mon Apr 07 21:30 PM", "arrival_hour": "21:30", "arrival_dm": "07 Apr", "arrival_format_html": "07/04/2025", "image_url": "", "travelClass": []}], "price": "7,049.62฿", "adult_price": 6687.5, "adult_price_html": "6,687.50฿", "total_adult_price": "6,687.50฿", "total_child_price": "0.00฿", "total_infant_price": "0.00฿", "adult_count": 1, "child_count": 0, "infant_count": 0, "priceIncludesTax": false, "discount": false, "departureDate": "2025-04-07", "base_price": "6,250.00฿", "can_book": true, "from_api": true, "enDelayTime": "1h 40m", "duration": 100, "enRouteHours": "01h 40m", "enRouteDays": "", "fare_types": {"Eco": {"bookingKey": "AoTQBurpMIHnf9uG81eƒljD13Vx¥dtvp08NcnoWYHLWUR4hRQYPr9kBbw7vCudySt6H88hswxTVszERCw13x3bp5ƒ2hUyYpRNTbVneNpuUHOWrCuwoBdrARIx5GmkBiX2fGGZjTfxgYsLOBnXRzBKlBB2PBJHxfQQs1L39IkfLs9ƒFG65xSqC1VEGjEy2EYloUkVcFhgFfHwIP30oL4rHoiylp5oOBnQawhuiƒdƒ1yƒ3WEPQeTkh9f8yV7xDWzd30V8Jusl9YdshZbAYdlwBMKRQERy4qCƒxFDR4DKP7EbKXfDO7ƒ9rKv8JNzPG12q3FZRjbktGw84XTYYfT5YLGUGOee6kDneqoAUDgcj5ux6CX4IA3PqexAuPXSPvZhds6MfKbYƒN4PvkijJsEIV2ImIBMvvZ5H7R5SZ7qUgM¥e81L2E7YwvNGjl1bAlhZUZ8jb0wMhfWr8ZzM0bvJ2¥fb9tJUDaGZCEdMHQJjYsdB4KilC5dIpQtVY9PMaƒssnEJUXM1AkAqv9LJU73hY3FbsfglƒOBoloznoCTMfddJ73EI=", "totalFare": 7049.62, "totalDiscount": 0, "allFareWithoutBag": 0, "totalAdultFare": 6687.5, "totalChildFare": 0, "totalInfantFare": 0, "adultCount": 1, "childCount": 0, "infantCount": 0, "priceIncludesTax": false, "basePrice": 6250, "adultFare": 6687.5, "adult_price": 6687.5, "currency_code": "THB", "childFare": 0, "infantFare": 0, "tax": 452.69, "name": "Eco", "code": "Eco", "description": "<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\"><strong style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset;\">Including:</strong></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">7kg carry-on baggage </span></p><p>* Operated by : Avion Express</p>", "order": 4, "title_color": "#6ab72e", "background_color": "#e1f7bf", "logo": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/qkaXPBELoUZYEfwZiYWIlitLxhcekOdMgf7Hy0SS.svg", "content": "<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\"><strong style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset;\">Including:</strong></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">7kg carry-on baggage </span></p><p>* Operated by : Avion Express</p>", "max_passengers": 18, "price_detail": {"fare_tax": 437.5, "tax_fee_tax": 15.19, "addon_tax": 0, "fare_base": 6250, "tax_fee_base": 346.93, "addon_base": 0, "fare_discount": 0, "tax_fee_discount": 0, "addon_discount": 0, "fare_total": 6687.5, "tax_fee_total": 362.12, "addon_total": 0, "tax_fee": {"AI": {"amount": 130, "baseAmount": 130, "taxAmount": 0, "discountAmount": 0, "count": 1, "name": "Airport Tax Thai Dom", "currency": "THB"}, "AM": {"amount": 164.78, "baseAmount": 154, "taxAmount": 10.78, "discountAmount": 0, "count": 1, "name": "Admin fee Dom Thai", "currency": "THB"}, "HA": {"amount": 67.34, "baseAmount": 62.93, "taxAmount": 4.41, "discountAmount": 0, "count": 1, "name": "Handling Fee - Fare", "currency": "THB"}}, "fare": {"adult": {"amount": 6687.5, "baseAmount": 6250, "taxAmount": 437.5, "discountAmount": 0, "count": 1, "name": "Eco1", "currency": "THB"}}}, "price_display_select_fare": "7,049.62", "totalBeforeDiscount": "7,049.62", "availability": 56}, "Deluxe": {"bookingKey": "AoTQBurpMIHnf9uG81eƒljD13Vx¥dtvp08NcnoWYHLWUR4hRQYPr9kBbw7vCudySt6H88hswxTVszERCw13x3bp5ƒ2hUyYpRNTbVneNpuUHOWrCuwoBdrARIx5GmkBiXh8EVre1CNBWPPKcNjsAAtzvZDu1dpnNw8PQN98S1BUxSFpHƒcƒFbErwVXRtY0ƒVdwxT3¥JvAsXh¥oQw68RddpRYYXYif1cbBRhEqxƒMD0g¥7KgcMZA3lU1Phs5eiquUjUgn¥MaiGEpXTXYDtmU6zyguPX8lrƒk2R0ykiGV9GMc1m6MxU¥SOeey45t7e68mkyNyQn8TjkQ4K3ca9719U3hXnSHsTSƒFJHjUHh¥ar1eF¥jmh8WupEwHyhiAfVNtUUvT1wfDK8Xq1bPP8Siez7KpEFqWOPLCRFT0hXJQezZImIoWjH7lbvLzcA¥yxEhƒxTbJnhwHBOIykjeAJ8v9sVtvcLoTXhSƒnky2MfƒJOJAmHfKeAcWX¥ApCbX602ex¥hjvuNu4RW0bzuXY9bcmz1Q¥YTfOus6In9u1OGBZJtqGGAQ=", "totalFare": 7691.62, "totalDiscount": 0, "allFareWithoutBag": 0, "totalAdultFare": 7329.5, "totalChildFare": 0, "totalInfantFare": 0, "adultCount": 1, "childCount": 0, "infantCount": 0, "priceIncludesTax": false, "basePrice": 6850, "adultFare": 7329.5, "adult_price": 7329.5, "currency_code": "THB", "childFare": 0, "infantFare": 0, "tax": 494.69, "name": "Deluxe", "code": "Deluxe", "description": "<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\"><strong style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset;\">Including:</strong></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">7kg carry-on baggage&nbsp;</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">20kg of checked baggage</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Seat selection (upon availability, excluding SkyBoss and SkyBoss Business seats)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Change of flight, date, or route are all permitted (fare difference applied, if any)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /> Priority Check-in Lane for Deluxe (not eligible for Vietjet Vietnam flight)</p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\">&nbsp;</p><p>* Operated by : Avion Express</p>", "order": 3, "title_color": "#f9a51a", "background_color": "#fff4bd", "logo": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/fW28o2REkpZTk1TVGVo5xWpuRm0ELCT2punE4wkn.svg", "content": "<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\"><strong style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset;\">Including:</strong></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">7kg carry-on baggage&nbsp;</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">20kg of checked baggage</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Seat selection (upon availability, excluding SkyBoss and SkyBoss Business seats)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Change of flight, date, or route are all permitted (fare difference applied, if any)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /> Priority Check-in Lane for Deluxe (not eligible for Vietjet Vietnam flight)</p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\">&nbsp;</p><p>* Operated by : Avion Express</p>", "max_passengers": 18, "price_detail": {"fare_tax": 479.5, "tax_fee_tax": 15.19, "addon_tax": 0, "fare_base": 6850, "tax_fee_base": 346.93, "addon_base": 0, "fare_discount": 0, "tax_fee_discount": 0, "addon_discount": 0, "fare_total": 7329.5, "tax_fee_total": 362.12, "addon_total": 0, "tax_fee": {"AI": {"amount": 130, "baseAmount": 130, "taxAmount": 0, "discountAmount": 0, "count": 1, "name": "Airport Tax Thai Dom", "currency": "THB"}, "AM": {"amount": 164.78, "baseAmount": 154, "taxAmount": 10.78, "discountAmount": 0, "count": 1, "name": "Admin fee Dom Thai", "currency": "THB"}, "HA": {"amount": 67.34, "baseAmount": 62.93, "taxAmount": 4.41, "discountAmount": 0, "count": 1, "name": "Handling Fee - Fare", "currency": "THB"}}, "fare": {"adult": {"amount": 7329.5, "baseAmount": 6850, "taxAmount": 479.5, "discountAmount": 0, "count": 1, "name": "Deluxe1", "currency": "THB"}}}, "price_display_select_fare": "7,691.62", "totalBeforeDiscount": "7,691.62", "availability": 56}, "SkyBoss": {"bookingKey": "AoTQBurpMIHnf9uG81eƒlhWrifb09NuAdmDTps4KKu2nfImuU4G9zF1voXppnƒoGCƒYksGBTKmm1DrQOh1LaogBfgcoj01Nyrho8OjzvUDOOzfGD¥IdXGjC593UnOssAkuJIP1qhFQeTƒjC43yli¥xxp6b9mfXel5HMDmT7pQkbbv7KA¥PkRKDHz8ƒZCon¥Unmf75NSDi¥iWoFCndUgvcYecy7Wƒ8erwƒRaiXS1RYv0kJd76bSR2XcYƒ2NZhv9VtNSnrlsW9GLoKx3RZ7DyoeyAIVG7wGyynOYH6ErJCuKEQ64y2S0VbNE8isYnYip¥7CPnisTwgQ6cbIO5qO1u3TOkakU1HnIWpIg5JsqwTcYYM09PSAYij2tg0IOB¥PZVI1bbcvvAVtƒ8DZgA90O2FiEMglvB5P6Vr9rbFcCJdPczaRvtw1ƒBngi2Xj2e2kBIizKPoHxobpoTUgnVSMacBN3ƒ8k0RY9chybiixrFrFuYQCƒ0eseMkBFSbf2RORyx6lwTd9iMzIQd5K4keq1X5¥yVgbFSVEVFnbNo4nAsJu8zY=", "totalFare": 8922.12, "totalDiscount": 0, "allFareWithoutBag": 0, "totalAdultFare": 8560, "totalChildFare": 0, "totalInfantFare": 0, "adultCount": 1, "childCount": 0, "infantCount": 0, "priceIncludesTax": false, "basePrice": 8000, "adultFare": 8560, "adult_price": 8560, "currency_code": "THB", "childFare": 0, "infantFare": 0, "tax": 575.19, "name": "SkyBoss", "code": "SkyBoss", "description": "<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\"><strong style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset;\">Including:</strong></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\">&nbsp;</p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">10kg carry-on baggage</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">30kg&nbsp;<span style=\"font-family: unset;\">of checked baggage and 01 (one) golf club set 15 kg (if any)</span></span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Access to the lounge at the airport (not applicable at airports where there is no standard lounge service)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority Check-in Lane</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority at security checkpoints (depending on airport conditions and facilities)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Private car service between departure or arrival gate and the aircraft (if there is no access to airbridge for the flight)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority for seat selection</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Complimentary food &amp; beverages served onboard</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Keep credit shell for unused ticket up to 24 months&nbsp; in case of no-show</span></p><p>* Operated by : Avion Express</p>", "order": 2, "title_color": "#da2128", "background_color": "#ffd3d5", "logo": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/Y4LTXsurHJqJ904k3Yo47x4Vxo2t47xhndjWH0qd.svg", "content": "<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\"><strong style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset;\">Including:</strong></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\">&nbsp;</p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">10kg carry-on baggage</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">30kg&nbsp;<span style=\"font-family: unset;\">of checked baggage and 01 (one) golf club set 15 kg (if any)</span></span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Access to the lounge at the airport (not applicable at airports where there is no standard lounge service)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority Check-in Lane</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority at security checkpoints (depending on airport conditions and facilities)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Private car service between departure or arrival gate and the aircraft (if there is no access to airbridge for the flight)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority for seat selection</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Complimentary food &amp; beverages served onboard</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Keep credit shell for unused ticket up to 24 months&nbsp; in case of no-show</span></p><p>* Operated by : Avion Express</p>", "max_passengers": 18, "price_detail": {"fare_tax": 560, "tax_fee_tax": 15.19, "addon_tax": 0, "fare_base": 8000, "tax_fee_base": 346.93, "addon_base": 0, "fare_discount": 0, "tax_fee_discount": 0, "addon_discount": 0, "fare_total": 8560, "tax_fee_total": 362.12, "addon_total": 0, "tax_fee": {"AI": {"amount": 130, "baseAmount": 130, "taxAmount": 0, "discountAmount": 0, "count": 1, "name": "Airport Tax Thai Dom", "currency": "THB"}, "AM": {"amount": 164.78, "baseAmount": 154, "taxAmount": 10.78, "discountAmount": 0, "count": 1, "name": "Admin fee Dom Thai", "currency": "THB"}, "HA": {"amount": 67.34, "baseAmount": 62.93, "taxAmount": 4.41, "discountAmount": 0, "count": 1, "name": "Handling Fee - Fare", "currency": "THB"}}, "fare": {"adult": {"amount": 8560, "baseAmount": 8000, "taxAmount": 560, "discountAmount": 0, "count": 1, "name": "SkyBoss", "currency": "THB"}}}, "price_display_select_fare": "8,922.12", "totalBeforeDiscount": "8,922.12", "availability": 12}, "Business": {"code": "Business", "order": 1, "title_color": "#af8903", "background_color": "#efe4bb", "logo": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/ghMi1WvoclTzNGXAfl9c2gPvkH7vcWWqyNqGXE1J.svg", "content": "<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\"><strong style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset;\">Including</strong></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">18kg carry-on baggage and 01 small bag not exceeding 2kg</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">40kg or 60kg of checked baggage and 01 (one) golf club set (if any)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Access to luxury lounge (not applicable on domestic flights in Thailand, and at airports where there is no standard lounge service)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority at check-in</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Baggage handling service privileges</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority at security checkpoints (depending on airport conditions and facilities)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Private car service between departure or arrival gate and the aircraft (in the event that there is no access to airbridge for the flight)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority seat selection</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Complimentary food &amp; beverage served onboard</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">03-in-01 amenities</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Complimentary SkyBoss Flight Care (not eligible for flights operated by Thai Vietjet)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Passengers entitled to credit shell (open ticket) valid for up to 24 months for unused tickets or in case of no-show</span></p>", "adult_price_html": null, "availability": 0}}, "flight_seat": null, "band": "VZ", "channel_provider": "AMELIA", "city_pair": "BKK-HKT", "transit": "direct", "airline": "VZ", "timeOfDay": "evening", "earliestDeparture": "2025-04-07 19:50", "latestArrivalLocal": "2025-04-07 21:30"}, "QcCbieEcFuYhONTHBja6Tƒ8ƒntFPuWwwFnsTr1y4rq3PBrXnomyOrSQvWKgGvXcOPSR8pjfpKn30Wv0uXTlJVfgHO¥mgGj5mDv36IEpCbHnVKW5uyNGm2AE6OVM8jofQyQIIZVEI4iOYc63F5avdAqUzBRfakvrd0NAKNF7idKqtrYvdudHmQ38gs5w670BWEd8aje¥lAPodsDDCOHTXƒBmLWo7e6AaBN4elGCLuVV8=1": {"index": 1, "id": "QcCbieEcFuYhONTHBja6Tƒ8ƒntFPuWwwFnsTr1y4rq3PBrXnomyOrSQvWKgGvXcOPSR8pjfpKn30Wv0uXTlJVfgHO¥mgGj5mDv36IEpCbHnVKW5uyNGm2AE6OVM8jofQyQIIZVEI4iOYc63F5avdAqUzBRfakvrd0NAKNF7idKqtrYvdudHmQ38gs5w670BWEd8aje¥lAPodsDDCOHTXƒBmLWo7e6AaBN4elGCLuVV8=", "name": "BangKok - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to Phuket", "route_name": "BKK → HKT", "numberOfStops": 1, "flights": [{"airline_code": "VZ", "airline_name": "Thai VietJet Air", "flight_number": "312", "aircraft_model": "320", "departure": "BangKok (Suvarnabhumi)", "departure_code": "BKK", "arrival": "Phuke<PERSON>", "arrival_code": "HKT", "trip_hour": "01h 35m", "departure_date": {"date": "2025-04-07 21:00:00.000000", "timezone_type": 3, "timezone": "Asia/Bangkok"}, "departure_date_html": "2025-04-07 21:00:00", "arrival_date": {"date": "2025-04-07 22:35:00.000000", "timezone_type": 3, "timezone": "Asia/Bangkok"}, "arrival_date_html": "2025-04-07 22:35:00", "departure_time": "Mon Apr 07 21:00 PM", "departure_time_html": "Mon, 07 Apr 2025", "departure_hour": "21:00", "departure_dm": "07 Apr", "departure_format_html": "07/04/2025", "arrival_time": "Mon Apr 07 22:35 PM", "arrival_hour": "22:35", "arrival_dm": "07 Apr", "arrival_format_html": "07/04/2025", "image_url": "", "travelClass": []}], "price": "1,421.42฿", "adult_price": 1059.3, "adult_price_html": "1,059.30฿", "total_adult_price": "1,059.30฿", "total_child_price": "0.00฿", "total_infant_price": "0.00฿", "adult_count": 1, "child_count": 0, "infant_count": 0, "priceIncludesTax": false, "discount": false, "departureDate": "2025-04-07", "base_price": "990.00฿", "can_book": true, "from_api": true, "enDelayTime": "1h 35m", "duration": 95, "enRouteHours": "01h 35m", "enRouteDays": "", "fare_types": {"Eco": {"bookingKey": "AoTQBurpMIHnf9uG81eƒlk0EBKuTJo0AKglDƒrNnClFzy4ƒKvLOn1DStKmNMlvP1Ifr0rƒUHdacp6KAL1yUp5yNJZp5v7pD4uMuPgieC0vhCfABrjkbaJuPE6WVWxTW8vk16ff9ADybT6duZgJdkiCWe2oQMr2ƒFHSZnBIzYewBqNF4auq2ykU6AG0J35lm5FcTP6Giegzrq4Q7VjMzFixEZpFrVBOOh2P6c4Xo9I6FJwCwBZ0Ii4wPq0mQ0UDUHKSmyofYFCMACwEY15YyAGgBLldJOomSo11C3TeeZƒQƒpkrFXqCov3U4KLypCK5NƒOS8u75NRtxQD¥xKlzxP7rƒ3YYz7xnxqNWVMuRjAka3POLoxGTn1RipHeIwSVZxilLhfQUR¥uDf952ƒm4tfeKOTwcY8jY1ƒMWaJ3O2MFMtbMQzdBIt5EZog4ukdAƒvv4YXusOtsd0MxPƒ5omEhs0cYemYƒpDTQiNQmJez2ikPyvayghluVgQKTZX3S3FOFfKBI2ƒDtIkPoc¥qtN9fMgYo8goKr7nu2jQRDcoxƒD8On1U=", "totalFare": 1421.42, "totalDiscount": 0, "allFareWithoutBag": 0, "totalAdultFare": 1059.3, "totalChildFare": 0, "totalInfantFare": 0, "adultCount": 1, "childCount": 0, "infantCount": 0, "priceIncludesTax": false, "basePrice": 990, "adultFare": 1059.3, "adult_price": 1059.3, "currency_code": "THB", "childFare": 0, "infantFare": 0, "tax": 84.49, "name": "Eco", "code": "Eco", "description": "<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\"><strong style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset;\">Including:</strong></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">7kg carry-on baggage </span></p>", "order": 4, "title_color": "#6ab72e", "background_color": "#e1f7bf", "logo": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/qkaXPBELoUZYEfwZiYWIlitLxhcekOdMgf7Hy0SS.svg", "content": "<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\"><strong style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset;\">Including:</strong></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">7kg carry-on baggage </span></p>", "max_passengers": 18, "price_detail": {"fare_tax": 69.3, "tax_fee_tax": 15.19, "addon_tax": 0, "fare_base": 990, "tax_fee_base": 346.93, "addon_base": 0, "fare_discount": 0, "tax_fee_discount": 0, "addon_discount": 0, "fare_total": 1059.3, "tax_fee_total": 362.12, "addon_total": 0, "tax_fee": {"AI": {"amount": 130, "baseAmount": 130, "taxAmount": 0, "discountAmount": 0, "count": 1, "name": "Airport Tax Thai Dom", "currency": "THB"}, "AM": {"amount": 164.78, "baseAmount": 154, "taxAmount": 10.78, "discountAmount": 0, "count": 1, "name": "Admin fee Dom Thai", "currency": "THB"}, "HA": {"amount": 67.34, "baseAmount": 62.93, "taxAmount": 4.41, "discountAmount": 0, "count": 1, "name": "Handling Fee - Fare", "currency": "THB"}}, "fare": {"adult": {"amount": 1059.3, "baseAmount": 990, "taxAmount": 69.3, "discountAmount": 0, "count": 1, "name": "Eco1", "currency": "THB"}}}, "price_display_select_fare": "1,421.42", "totalBeforeDiscount": "1,421.42", "availability": 3}, "Deluxe": {"bookingKey": "AoTQBurpMIHnf9uG81eƒlk0EBKuTJo0AKglDƒrNnClFzy4ƒKvLOn1DStKmNMlvP1Ifr0rƒUHdacp6KAL1yUp5yNJZp5v7pD4uMuPgieC0vhCfABrjkbaJuPE6WVWxTW8¥4Qh7y8FaON38HepL43uLbR2Sr6WU3EQSpQkeMK3tp1atoTGTjFi0kJmnUcw2OM¥dAlg9OlyubdZMALCJMgJ2kƒm80tFMeCnYQ0IBHNweAaBecil6GRqmF0s0VMPxTllcZsYS8Z7mwFJjkV8GJLno9ƒIqOy2ƒF¥Otq¥VBMS7JLWgD8PyqNJxkxMb0q7IxooaPPiZ4tHTqpMbIUyFkJekR1rXHRxQ91utS8¥kbSNlXUAkw85UAOIFƒWz¥4yo3Y5LUHq1hFLuGw3pCjNCnMHnvFu62zHxMLZhud2Ji8JEGuuf8dix2JsuL3¥w7XsHndbzAIEszAS5WVq3qlTZ04xTSmƒp3Ridkmx¥6vJyDrXqo573bhƒanSfJbkqHUI5UTH2akD¥gg8CdYVyKdimFse3l7Vc¥3nBYvHAYrqHIWsWMFH18=", "totalFare": 2213.22, "totalDiscount": 0, "allFareWithoutBag": 0, "totalAdultFare": 1851.1, "totalChildFare": 0, "totalInfantFare": 0, "adultCount": 1, "childCount": 0, "infantCount": 0, "priceIncludesTax": false, "basePrice": 1730, "adultFare": 1851.1, "adult_price": 1851.1, "currency_code": "THB", "childFare": 0, "infantFare": 0, "tax": 136.29, "name": "Deluxe", "code": "Deluxe", "description": "<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\"><strong style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset;\">Including:</strong></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">7kg carry-on baggage&nbsp;</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">20kg of checked baggage</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Seat selection (upon availability, excluding SkyBoss and SkyBoss Business seats)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Change of flight, date, or route are all permitted (fare difference applied, if any)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /> Priority Check-in Lane for Deluxe (not eligible for Vietjet Vietnam flight)</p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\">&nbsp;</p>", "order": 3, "title_color": "#f9a51a", "background_color": "#fff4bd", "logo": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/fW28o2REkpZTk1TVGVo5xWpuRm0ELCT2punE4wkn.svg", "content": "<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\"><strong style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset;\">Including:</strong></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">7kg carry-on baggage&nbsp;</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">20kg of checked baggage</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Seat selection (upon availability, excluding SkyBoss and SkyBoss Business seats)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Change of flight, date, or route are all permitted (fare difference applied, if any)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /> Priority Check-in Lane for Deluxe (not eligible for Vietjet Vietnam flight)</p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\">&nbsp;</p>", "max_passengers": 18, "price_detail": {"fare_tax": 121.1, "tax_fee_tax": 15.19, "addon_tax": 0, "fare_base": 1730, "tax_fee_base": 346.93, "addon_base": 0, "fare_discount": 0, "tax_fee_discount": 0, "addon_discount": 0, "fare_total": 1851.1, "tax_fee_total": 362.12, "addon_total": 0, "tax_fee": {"AI": {"amount": 130, "baseAmount": 130, "taxAmount": 0, "discountAmount": 0, "count": 1, "name": "Airport Tax Thai Dom", "currency": "THB"}, "AM": {"amount": 164.78, "baseAmount": 154, "taxAmount": 10.78, "discountAmount": 0, "count": 1, "name": "Admin fee Dom Thai", "currency": "THB"}, "HA": {"amount": 67.34, "baseAmount": 62.93, "taxAmount": 4.41, "discountAmount": 0, "count": 1, "name": "Handling Fee - Fare", "currency": "THB"}}, "fare": {"adult": {"amount": 1851.1, "baseAmount": 1730, "taxAmount": 121.1, "discountAmount": 0, "count": 1, "name": "Deluxe1", "currency": "THB"}}}, "price_display_select_fare": "2,213.22", "totalBeforeDiscount": "2,213.22", "availability": 3}, "SkyBoss": {"bookingKey": "AoTQBurpMIHnf9uG81eƒlqAVrZIbuquBWZKjdmc6jTƒ3JP5KXEBi5QWPEn980QTe¥0TC¥forNyn6lB94VS8BuMOTUVrgzjCJZuLRnoyfN380Q5AGIcT3DbFWGLMGsaFDo85Fa3Nn9MzxJPipDoOXW7nqUNQFnySWlg55uuk6eOt¥ƒƒRhƒVr33iUCpouNUTƒ4¥ƒ1LMmWg0Xq7VYVLLcMBG4oqa2MfrƒigUIAClDRWAIPNApUIoa8afs8QbXMmgyvyjZKQDfECLbwx5YggquY5x5jbRjGS4F29GG2LYT2eBXR2xUfRNIfO3K9GAbzMHbƒF2s7Onl6PQMQyAJcwzLzfMQBkFQfJw3b9bEƒzcOfgPf0sDvyVW4gSm335woBrsSTOvWeW75AiVDXHq¥ubci6ƒLNDLeIORbd7vQgq1exX9sD0XcRayNI7vhZ246W6cQzVH28eu8Sj0C1ydHQpUAf7vA9FƒwkrbƒRjkiForYGDbZ4JylGwrkDNsL5UYM2jfFKk4kY6T4KzjndrocEqQJyIAnYIdIKugoafQhDmvtFL7Kdg=", "totalFare": 5284.12, "totalDiscount": 0, "allFareWithoutBag": 0, "totalAdultFare": 4922, "totalChildFare": 0, "totalInfantFare": 0, "adultCount": 1, "childCount": 0, "infantCount": 0, "priceIncludesTax": false, "basePrice": 4600, "adultFare": 4922, "adult_price": 4922, "currency_code": "THB", "childFare": 0, "infantFare": 0, "tax": 337.19, "name": "SkyBoss", "code": "SkyBoss", "description": "<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\"><strong style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset;\">Including:</strong></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\">&nbsp;</p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">10kg carry-on baggage</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">30kg&nbsp;<span style=\"font-family: unset;\">of checked baggage and 01 (one) golf club set 15 kg (if any)</span></span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Access to the lounge at the airport (not applicable at airports where there is no standard lounge service)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority Check-in Lane</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority at security checkpoints (depending on airport conditions and facilities)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Private car service between departure or arrival gate and the aircraft (if there is no access to airbridge for the flight)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority for seat selection</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Complimentary food &amp; beverages served onboard</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Keep credit shell for unused ticket up to 24 months&nbsp; in case of no-show</span></p>", "order": 2, "title_color": "#da2128", "background_color": "#ffd3d5", "logo": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/Y4LTXsurHJqJ904k3Yo47x4Vxo2t47xhndjWH0qd.svg", "content": "<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\"><strong style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset;\">Including:</strong></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\">&nbsp;</p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">10kg carry-on baggage</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">30kg&nbsp;<span style=\"font-family: unset;\">of checked baggage and 01 (one) golf club set 15 kg (if any)</span></span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Access to the lounge at the airport (not applicable at airports where there is no standard lounge service)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority Check-in Lane</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority at security checkpoints (depending on airport conditions and facilities)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Private car service between departure or arrival gate and the aircraft (if there is no access to airbridge for the flight)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority for seat selection</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Complimentary food &amp; beverages served onboard</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Keep credit shell for unused ticket up to 24 months&nbsp; in case of no-show</span></p>", "max_passengers": 18, "price_detail": {"fare_tax": 322, "tax_fee_tax": 15.19, "addon_tax": 0, "fare_base": 4600, "tax_fee_base": 346.93, "addon_base": 0, "fare_discount": 0, "tax_fee_discount": 0, "addon_discount": 0, "fare_total": 4922, "tax_fee_total": 362.12, "addon_total": 0, "tax_fee": {"AI": {"amount": 130, "baseAmount": 130, "taxAmount": 0, "discountAmount": 0, "count": 1, "name": "Airport Tax Thai Dom", "currency": "THB"}, "AM": {"amount": 164.78, "baseAmount": 154, "taxAmount": 10.78, "discountAmount": 0, "count": 1, "name": "Admin fee Dom Thai", "currency": "THB"}, "HA": {"amount": 67.34, "baseAmount": 62.93, "taxAmount": 4.41, "discountAmount": 0, "count": 1, "name": "Handling Fee - Fare", "currency": "THB"}}, "fare": {"adult": {"amount": 4922, "baseAmount": 4600, "taxAmount": 322, "discountAmount": 0, "count": 1, "name": "SkyBoss", "currency": "THB"}}}, "price_display_select_fare": "5,284.12", "totalBeforeDiscount": "5,284.12", "availability": 2}, "Business": {"code": "Business", "order": 1, "title_color": "#af8903", "background_color": "#efe4bb", "logo": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/ghMi1WvoclTzNGXAfl9c2gPvkH7vcWWqyNqGXE1J.svg", "content": "<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\"><strong style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset;\">Including</strong></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">18kg carry-on baggage and 01 small bag not exceeding 2kg</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">40kg or 60kg of checked baggage and 01 (one) golf club set (if any)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Access to luxury lounge (not applicable on domestic flights in Thailand, and at airports where there is no standard lounge service)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority at check-in</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Baggage handling service privileges</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority at security checkpoints (depending on airport conditions and facilities)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Private car service between departure or arrival gate and the aircraft (in the event that there is no access to airbridge for the flight)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority seat selection</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Complimentary food &amp; beverage served onboard</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">03-in-01 amenities</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Complimentary SkyBoss Flight Care (not eligible for flights operated by Thai Vietjet)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Passengers entitled to credit shell (open ticket) valid for up to 24 months for unused tickets or in case of no-show</span></p>", "adult_price_html": null, "availability": 0}}, "flight_seat": null, "band": "VZ", "channel_provider": "AMELIA", "city_pair": "BKK-HKT", "transit": "direct", "airline": "VZ", "timeOfDay": "evening", "earliestDeparture": "2025-04-07 21:00", "latestArrivalLocal": "2025-04-07 22:35"}, "QcCbieEcFuYhONTHBja6Tƒ8ƒntFPuWwwFnsTr1y4rq3PBrXnomyOrSQvWKgGvXcOPSR8pjfpKn30Wv0uXTlJVfgHO¥mgGj5mDv36IEpCbHnVKW5uyNGm2AE6OVM8jofQyQIIZVEI4iOYc63F5avdAo7njXRPFlda8rBZanKCw5L7o5¥58MnBUhNt6CxJomsnw9vKUNqm2cXtDHQeKJ59K4OnihNX7SqO5MHYwQkSTUQ=2": {"index": 2, "id": "QcCbieEcFuYhONTHBja6Tƒ8ƒntFPuWwwFnsTr1y4rq3PBrXnomyOrSQvWKgGvXcOPSR8pjfpKn30Wv0uXTlJVfgHO¥mgGj5mDv36IEpCbHnVKW5uyNGm2AE6OVM8jofQyQIIZVEI4iOYc63F5avdAo7njXRPFlda8rBZanKCw5L7o5¥58MnBUhNt6CxJomsnw9vKUNqm2cXtDHQeKJ59K4OnihNX7SqO5MHYwQkSTUQ=", "name": "BangKok - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to Phuket", "route_name": "BKK → HKT", "numberOfStops": 1, "flights": [{"airline_code": "VZ", "airline_name": "Thai VietJet Air", "flight_number": "316", "aircraft_model": "320", "departure": "BangKok (Suvarnabhumi)", "departure_code": "BKK", "arrival": "Phuke<PERSON>", "arrival_code": "HKT", "trip_hour": "01h 40m", "departure_date": {"date": "2025-04-07 22:00:00.000000", "timezone_type": 3, "timezone": "Asia/Bangkok"}, "departure_date_html": "2025-04-07 22:00:00", "arrival_date": {"date": "2025-04-07 23:40:00.000000", "timezone_type": 3, "timezone": "Asia/Bangkok"}, "arrival_date_html": "2025-04-07 23:40:00", "departure_time": "Mon Apr 07 22:00 PM", "departure_time_html": "Mon, 07 Apr 2025", "departure_hour": "22:00", "departure_dm": "07 Apr", "departure_format_html": "07/04/2025", "arrival_time": "Mon Apr 07 23:40 PM", "arrival_hour": "23:40", "arrival_dm": "07 Apr", "arrival_format_html": "07/04/2025", "image_url": "", "travelClass": []}], "price": "1,421.42฿", "adult_price": 1059.3, "adult_price_html": "1,059.30฿", "total_adult_price": "1,059.30฿", "total_child_price": "0.00฿", "total_infant_price": "0.00฿", "adult_count": 1, "child_count": 0, "infant_count": 0, "priceIncludesTax": false, "discount": false, "departureDate": "2025-04-07", "base_price": "990.00฿", "can_book": true, "from_api": true, "enDelayTime": "1h 40m", "duration": 100, "enRouteHours": "01h 40m", "enRouteDays": "", "fare_types": {"Eco": {"bookingKey": "AoTQBurpMIHnf9uG81eƒlljk3FU2SninrGOHFlumMPu4zpNS4cmEYZ¥HY23lIGJ51d9k0nqyiRwDGPxqq66kfJiM6DQGpxv06yJDiKrVv5fpDFXMPmNaKcxpsDsDPZW7A¥msVCqu¥BGgw4JVYbrQroM¥nlzMp5Di0nYzsUK34WDeuLfeax30yqMl3xMZ9nkB7JB1Vn8RqXDEAft38N4OFSTvorw1J5qs2vQCkgNYaxXdfK7LXeCaLKtcqQld8sZq2moYDK3b8ntd26CwmVPOXysMQefknEj6g¥w2l¥1yJGRPz¥LOrpF¥iUFbmWMQzXU3NYBt8orahYbU8¥FXO5UasO2r1zwBXdwkgzppcPWAQJDXlJhHfN4OKRScogvyRLRyY9Iz2geJoUOorX0GehNMDR0EsGaFrhD2k4ITgTLTXnGMoKKcdbyeUJSO73kLtA9HlPDWqVrOaZqcYNNdFEIvRZitefOKxft0MWktcD1KSƒ13BprazwFWqjhSJA0E9ndBZbfpliCaFioOS5RY9PjKQUx¥lIrƒqqzZCTYX9sXlGLA=", "totalFare": 1421.42, "totalDiscount": 0, "allFareWithoutBag": 0, "totalAdultFare": 1059.3, "totalChildFare": 0, "totalInfantFare": 0, "adultCount": 1, "childCount": 0, "infantCount": 0, "priceIncludesTax": false, "basePrice": 990, "adultFare": 1059.3, "adult_price": 1059.3, "currency_code": "THB", "childFare": 0, "infantFare": 0, "tax": 84.49, "name": "Eco", "code": "Eco", "description": "<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\"><strong style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset;\">Including:</strong></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">7kg carry-on baggage </span></p>", "order": 4, "title_color": "#6ab72e", "background_color": "#e1f7bf", "logo": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/qkaXPBELoUZYEfwZiYWIlitLxhcekOdMgf7Hy0SS.svg", "content": "<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\"><strong style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset;\">Including:</strong></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">7kg carry-on baggage </span></p>", "max_passengers": 18, "price_detail": {"fare_tax": 69.3, "tax_fee_tax": 15.19, "addon_tax": 0, "fare_base": 990, "tax_fee_base": 346.93, "addon_base": 0, "fare_discount": 0, "tax_fee_discount": 0, "addon_discount": 0, "fare_total": 1059.3, "tax_fee_total": 362.12, "addon_total": 0, "tax_fee": {"AI": {"amount": 130, "baseAmount": 130, "taxAmount": 0, "discountAmount": 0, "count": 1, "name": "Airport Tax Thai Dom", "currency": "THB"}, "AM": {"amount": 164.78, "baseAmount": 154, "taxAmount": 10.78, "discountAmount": 0, "count": 1, "name": "Admin fee Dom Thai", "currency": "THB"}, "HA": {"amount": 67.34, "baseAmount": 62.93, "taxAmount": 4.41, "discountAmount": 0, "count": 1, "name": "Handling Fee - Fare", "currency": "THB"}}, "fare": {"adult": {"amount": 1059.3, "baseAmount": 990, "taxAmount": 69.3, "discountAmount": 0, "count": 1, "name": "Eco1", "currency": "THB"}}}, "price_display_select_fare": "1,421.42", "totalBeforeDiscount": "1,421.42", "availability": 1}, "Deluxe": {"bookingKey": "AoTQBurpMIHnf9uG81eƒlljk3FU2SninrGOHFlumMPu4zpNS4cmEYZ¥HY23lIGJ51d9k0nqyiRwDGPxqq66kfJiM6DQGpxv06yJDiKrVv5fpDFXMPmNaKcxpsDsDPZW7fVoAePtYfiQV85DHZ28GtQSsWPRoDN7CaP9zcx6J72R1ql0iwVzoGPƒfE4qv9wpmfaSw0DxoRbcxYaXwhek42RvPkTdJ4ffjoXPVrygqeDAms¥v24Ogƒ5hT2pum6IbwR3EjgnZwKV2tKLYƒƒHa3OLRƒUtTXrLk17S6HxeDoYk626eL3jlMuKIjRƒbxMCaOvnumqsGgdo1Y2JJzth7uwNyFrAyMcvQ¥ygReJiG81H¥93quƒmq1qMXivxrxNUSDZBJJRSbjlgCAEa8GPHnKByJHpVƒza2FNy4wK89jmWmndP3bBKdkmAFPNodEcBuL6fSuS5bJwjIPdgZJngaTT5xY7bQOWoezDWXa5u0g9uKIh0I7rIV6lLDs3nONaS9zQkUH95jCBqMIYRTC5fLaKtoEv¥719ngfhOAZbvwIcuhzItw=", "totalFare": 2213.22, "totalDiscount": 0, "allFareWithoutBag": 0, "totalAdultFare": 1851.1, "totalChildFare": 0, "totalInfantFare": 0, "adultCount": 1, "childCount": 0, "infantCount": 0, "priceIncludesTax": false, "basePrice": 1730, "adultFare": 1851.1, "adult_price": 1851.1, "currency_code": "THB", "childFare": 0, "infantFare": 0, "tax": 136.29, "name": "Deluxe", "code": "Deluxe", "description": "<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\"><strong style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset;\">Including:</strong></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">7kg carry-on baggage&nbsp;</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">20kg of checked baggage</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Seat selection (upon availability, excluding SkyBoss and SkyBoss Business seats)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Change of flight, date, or route are all permitted (fare difference applied, if any)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /> Priority Check-in Lane for Deluxe (not eligible for Vietjet Vietnam flight)</p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\">&nbsp;</p>", "order": 3, "title_color": "#f9a51a", "background_color": "#fff4bd", "logo": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/fW28o2REkpZTk1TVGVo5xWpuRm0ELCT2punE4wkn.svg", "content": "<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\"><strong style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset;\">Including:</strong></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">7kg carry-on baggage&nbsp;</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">20kg of checked baggage</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Seat selection (upon availability, excluding SkyBoss and SkyBoss Business seats)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Change of flight, date, or route are all permitted (fare difference applied, if any)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /> Priority Check-in Lane for Deluxe (not eligible for Vietjet Vietnam flight)</p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\">&nbsp;</p>", "max_passengers": 18, "price_detail": {"fare_tax": 121.1, "tax_fee_tax": 15.19, "addon_tax": 0, "fare_base": 1730, "tax_fee_base": 346.93, "addon_base": 0, "fare_discount": 0, "tax_fee_discount": 0, "addon_discount": 0, "fare_total": 1851.1, "tax_fee_total": 362.12, "addon_total": 0, "tax_fee": {"AI": {"amount": 130, "baseAmount": 130, "taxAmount": 0, "discountAmount": 0, "count": 1, "name": "Airport Tax Thai Dom", "currency": "THB"}, "AM": {"amount": 164.78, "baseAmount": 154, "taxAmount": 10.78, "discountAmount": 0, "count": 1, "name": "Admin fee Dom Thai", "currency": "THB"}, "HA": {"amount": 67.34, "baseAmount": 62.93, "taxAmount": 4.41, "discountAmount": 0, "count": 1, "name": "Handling Fee - Fare", "currency": "THB"}}, "fare": {"adult": {"amount": 1851.1, "baseAmount": 1730, "taxAmount": 121.1, "discountAmount": 0, "count": 1, "name": "Deluxe1", "currency": "THB"}}}, "price_display_select_fare": "2,213.22", "totalBeforeDiscount": "2,213.22", "availability": 1}, "SkyBoss": {"bookingKey": "AoTQBurpMIHnf9uG81eƒlpffwAGK2g4OCKEfUVzetlbCeqzk¥AQ09BS1uz5TdƒYƒvpycw41ƒRgE5szq5ƒƒJ4XRDU6OnJbXL0ƒSqcoB¥¥Qyw99XbailX0veDpw92Elƒmr3hFx2otkOuXvdFƒEMwƒjTRkJj75g770NHdjPMJ6¥uQXoyazdVuQvQIEdURYIJKiVzZrvcEKgsIW1OkojmPAiJyolqiRE16xJYJCsMU¥¥wu7xgUjIH3E36dƒkyqeHGwAStƒDKZcJ4NuTYƒMQ0ODyAFQQpHh0InUtH52kmihuZmvch5U856cXTN2F¥uSchAN¥E5¥aIjnAFiZ¥524hMzuiMrkKQiaAG7NhxV¥MQmAK7uFMgTOyRGov0n7E¥VMSeAƒbafb46FENCHPWoDZgRFPH2Z6fHJXZYU7mGUiAUMk61XL0ƒgEa8VRL0bt5NWUyipB1JcCYM2iKdm3Qhti¥wGFLlty1b6fNHzmqGv4QmƒKHuIPViAxFaczb6HzdGWH85a2EJk8Hk97lo3gS¥SW2e0nmIDRQXHhpPƒGJWqkStvqROsaI=", "totalFare": 5284.12, "totalDiscount": 0, "allFareWithoutBag": 0, "totalAdultFare": 4922, "totalChildFare": 0, "totalInfantFare": 0, "adultCount": 1, "childCount": 0, "infantCount": 0, "priceIncludesTax": false, "basePrice": 4600, "adultFare": 4922, "adult_price": 4922, "currency_code": "THB", "childFare": 0, "infantFare": 0, "tax": 337.19, "name": "SkyBoss", "code": "SkyBoss", "description": "<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\"><strong style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset;\">Including:</strong></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\">&nbsp;</p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">10kg carry-on baggage</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">30kg&nbsp;<span style=\"font-family: unset;\">of checked baggage and 01 (one) golf club set 15 kg (if any)</span></span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Access to the lounge at the airport (not applicable at airports where there is no standard lounge service)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority Check-in Lane</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority at security checkpoints (depending on airport conditions and facilities)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Private car service between departure or arrival gate and the aircraft (if there is no access to airbridge for the flight)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority for seat selection</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Complimentary food &amp; beverages served onboard</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Keep credit shell for unused ticket up to 24 months&nbsp; in case of no-show</span></p>", "order": 2, "title_color": "#da2128", "background_color": "#ffd3d5", "logo": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/Y4LTXsurHJqJ904k3Yo47x4Vxo2t47xhndjWH0qd.svg", "content": "<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\"><strong style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset;\">Including:</strong></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\">&nbsp;</p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">10kg carry-on baggage</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">30kg&nbsp;<span style=\"font-family: unset;\">of checked baggage and 01 (one) golf club set 15 kg (if any)</span></span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Access to the lounge at the airport (not applicable at airports where there is no standard lounge service)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority Check-in Lane</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority at security checkpoints (depending on airport conditions and facilities)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Private car service between departure or arrival gate and the aircraft (if there is no access to airbridge for the flight)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority for seat selection</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Complimentary food &amp; beverages served onboard</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Keep credit shell for unused ticket up to 24 months&nbsp; in case of no-show</span></p>", "max_passengers": 18, "price_detail": {"fare_tax": 322, "tax_fee_tax": 15.19, "addon_tax": 0, "fare_base": 4600, "tax_fee_base": 346.93, "addon_base": 0, "fare_discount": 0, "tax_fee_discount": 0, "addon_discount": 0, "fare_total": 4922, "tax_fee_total": 362.12, "addon_total": 0, "tax_fee": {"AI": {"amount": 130, "baseAmount": 130, "taxAmount": 0, "discountAmount": 0, "count": 1, "name": "Airport Tax Thai Dom", "currency": "THB"}, "AM": {"amount": 164.78, "baseAmount": 154, "taxAmount": 10.78, "discountAmount": 0, "count": 1, "name": "Admin fee Dom Thai", "currency": "THB"}, "HA": {"amount": 67.34, "baseAmount": 62.93, "taxAmount": 4.41, "discountAmount": 0, "count": 1, "name": "Handling Fee - Fare", "currency": "THB"}}, "fare": {"adult": {"amount": 4922, "baseAmount": 4600, "taxAmount": 322, "discountAmount": 0, "count": 1, "name": "SkyBoss", "currency": "THB"}}}, "price_display_select_fare": "5,284.12", "totalBeforeDiscount": "5,284.12", "availability": 2}, "Business": {"code": "Business", "order": 1, "title_color": "#af8903", "background_color": "#efe4bb", "logo": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/ghMi1WvoclTzNGXAfl9c2gPvkH7vcWWqyNqGXE1J.svg", "content": "<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px;\"><strong style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset;\">Including</strong></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">18kg carry-on baggage and 01 small bag not exceeding 2kg</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">40kg or 60kg of checked baggage and 01 (one) golf club set (if any)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Access to luxury lounge (not applicable on domestic flights in Thailand, and at airports where there is no standard lounge service)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority at check-in</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Baggage handling service privileges</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority at security checkpoints (depending on airport conditions and facilities)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Private car service between departure or arrival gate and the aircraft (in the event that there is no access to airbridge for the flight)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Priority seat selection</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Complimentary food &amp; beverage served onboard</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">03-in-01 amenities</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Complimentary SkyBoss Flight Care (not eligible for flights operated by Thai Vietjet)</span></p>\r\n<p style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: KoHo, sans-serif; font-size: 16px; margin-block: 0px; padding: 5px 0px; display: flex;\"><img style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; width: 14px; height: 14px; vertical-align: sub; line-height: 0; margin-right: 5px;\" src=\"https://cms-uat.s3.ap-southeast-1.amazonaws.com/greensuccess16x16-1606814406304.png\" alt=\"check\" /><span style=\"box-sizing: border-box; outline: 0px; scroll-behavior: smooth; font-family: unset; line-height: 1.1; display: inline-block;\">Passengers entitled to credit shell (open ticket) valid for up to 24 months for unused tickets or in case of no-show</span></p>", "adult_price_html": null, "availability": 0}}, "flight_seat": null, "band": "VZ", "channel_provider": "AMELIA", "city_pair": "BKK-HKT", "transit": "direct", "airline": "VZ", "timeOfDay": "evening", "earliestDeparture": "2025-04-07 22:00", "latestArrivalLocal": "2025-04-07 23:40"}}, "return_list": [], "airline_info": [], "markers": [], "expired_booking": "30", "url_back": "https://th.vietjetair.com/flight", "buyer_fees": [], "cityPairSearch": {"from_where": {"id": "BKK", "code": "BKK", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(BKK)", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "orders": 1, "first_order": 0, "second_order": 1, "country": "Thailand", "location_id": 1, "destination": ["ADL", "AKJ", "AMD", "BAV", "BER", "BFV", "BLR", "BNE", "BOM", "CAN", "CEB", "CEI", "CGD", "CGO", "CKG", "CNX", "COK", "CSX", "CTS", "CXR", "CZX", "DAD", "DAT", "DEL", "DPS", "DYG", "FOC", "FUK", "GAY", "HAK", "HAN", "HDY", "HET", "HFE", "HGH", "HIA", "HIJ", "HKG", "HKT", "HNA", "HND", "HPH", "HSG", "HUI", "HUN", "HYD", "IBR", "ICN", "INC", "JAI", "KBV", "KCZ", "KHN", "KIJ", "KIX", "KKC", "KMG", "KMQ", "KUL", "KWE", "KWL", "LPQ", "LYI", "MEL", "MFM", "MNL", "MZG", "NGB", "NGO", "NKG", "NRT", "OKA", "OKJ", "PER", "PKX", "PNH", "PQC", "PUS", "PVG", "RGN", "RMQ", "SDJ", "SGN", "SHE", "SIN", "SJW", "SWA", "SYD", "TAK", "TFU", "THD", "TPE", "TRZ", "UBN", "UBP", "URT", "UTH", "VCA", "VII", "VNS", "WUX", "XUZ", "YIH", "YTY"]}, "to_where": {"id": "HKT", "code": "HKT", "text": "Phuket(HKT)", "title": "Phuke<PERSON>", "orders": 2, "first_order": 0, "second_order": 1, "country": "Thailand", "location_id": 107, "destination": ["ADL", "AMD", "ATQ", "BKK", "BLR", "BNE", "BOM", "CAN", "CEI", "CGO", "CNX", "COK", "CSX", "CXR", "DAD", "DEL", "FUK", "HAN", "HET", "HIJ", "HND", "HYD", "ICN", "KIX", "KKC", "LKO", "MEL", "MNL", "NGO", "NRT", "OSL", "PER", "PNH", "PNQ", "PQC", "PUS", "PVG", "SGN", "SHJ", "SIN", "STV", "SYD", "TFU", "TNA", "TPE", "TRZ", "UBP", "UTH", "XIY", "XNN", "YIH"]}}, "fare_class_list": [{"code": "Business", "name": "Business", "sort": 1, "logo": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/ghMi1WvoclTzNGXAfl9c2gPvkH7vcWWqyNqGXE1J.svg", "title_color": "#af8903", "background_color": "#efe4bb", "lowest_fare_first": true}, {"code": "SkyBoss", "name": "SkyBoss", "sort": 2, "logo": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/Y4LTXsurHJqJ904k3Yo47x4Vxo2t47xhndjWH0qd.svg", "title_color": "#da2128", "background_color": "#ffd3d5", "lowest_fare_first": true}, {"code": "Deluxe", "name": "Deluxe", "sort": 3, "logo": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/fW28o2REkpZTk1TVGVo5xWpuRm0ELCT2punE4wkn.svg", "title_color": "#f9a51a", "background_color": "#fff4bd", "lowest_fare_first": true}, {"code": "Eco", "name": "Eco", "sort": 4, "logo": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/qkaXPBELoUZYEfwZiYWIlitLxhcekOdMgf7Hy0SS.svg", "title_color": "#6ab72e", "background_color": "#e1f7bf", "lowest_fare_first": true}], "paramsSearch": {"tripType": "onewaytrip", "adultCount": "1", "childCount": "0", "infantCount": "0"}, "promoCode": "", "promoCodeDiscount": "", "api_uuid": "8Fwev9od8JyowaSKbGDxBuITvJBdNWIcDOi8CfJz"}, "status": 1, "message": "founded"}