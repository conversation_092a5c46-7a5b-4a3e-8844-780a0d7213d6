from loguru import logger
import orjson
import pytest

from app import vz_tasks
from app.clients.vz_client import VZClient
from app.services import vz_search_service


@pytest.fixture
def vz_search():
    return VZClient()

def test_search_task():
    task_params = {
        'unique_id': '0a33332d4191b5c14dd0452ac17884bb',
        'site_code': 'vietjetair',
        'site_type': 'airline',
        'dep_airport_code': 'BKK',
        'arr_airport_code': 'CEI',
        'dep_date': '2025-04-26',
        'return_date': '',
        'trip_type': 'ow',
        'status': 'pending',
        'airline_code': 'VZ',
        'schedule_id': 133,
        'fetch_rule_id': 7,
        'expire_seconds': 600,
        'task_key': 'VZ-BKK-CEI-2025-04-26',
        'currency_code': 'THB',
        'create_time': '2025-04-03T17:27:33',
    }
    vz_tasks.search(task_params)

def test_flight_parse():
    search_r = None
    with open('tests/vz_search_result.json', 'r') as f:
        search_r = orjson.loads(f.read())
    result = vz_search_service.parse_flight(search_r, 1, 0, 0)
    logger.debug(result)


def test_send_task():
    task_params = {
        'unique_id': '0a33332d4191b5c14dd0452ac17884bb',
        'site_code': 'vietjetair',
        'site_type': 'airline',
        'dep_airport_code': 'BKK',
        'arr_airport_code': 'CEI',
        'dep_date': '2024-12-26',
        'return_date': '',
        'trip_type': 'ow',
        'status': 'pending',
        'airline_code': 'VZ',
        'schedule_id': 133,
        'fetch_rule_id': 7,
        'expire_seconds': 600,
        'task_key': 'VZ-BKK-CEI-2024-12-26',
        'currency_code': 'THB',
        'create_time': '2024-12-24T17:27:33',
    }
    task = vz_tasks.celery_app.signature("vz_search_task", args=(task_params,), queue='vz_search_queue')
    callback = vz_tasks.celery_app.signature("vz_search_callback_task", queue='vz_search_queue')

    # 调度任务
    task.apply_async(link=callback)
