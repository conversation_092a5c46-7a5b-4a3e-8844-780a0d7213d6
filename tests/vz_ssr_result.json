{"message": "Get SSR options successfully", "data": {"departure": {"index": 0, "id": "QcCbieEcFuYhONTHBja6TxPB8PiZ¥Jctv2LkQKGSmezrPd1UibvHlCwzVqwtXNiZS4¥yArXvSfm5qtzvmXY9AjW0RyDYK6cmZgcIYV3QUhJwzsXrPzRh5HrgKJmG1yh9ItaIx42DPqUnCoJN9PwqUN4Hx¥Ubgx8okMjtseh¥scUKdHhnunM7CWAynk8EgYIjaƒAg4MKg6qaB6fnQBI7gAUpK59kCdMqlZFIfUqy5J1M=", "name": "BangKok - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to Udon Thani", "route_name": "BKK → UTH", "numberOfStops": 1, "flights": [{"airline_code": "VZ", "airline_name": "Thai VietJet Air", "flight_number": "200", "aircraft_model": "320", "departure": "BangKok (Suvarnabhumi)", "departure_code": "BKK", "arrival": "<PERSON><PERSON>", "arrival_code": "UTH", "trip_hour": "01h 10m", "departure_date": {"date": "2025-01-05 08:30:00.000000", "timezone_type": 3, "timezone": "Asia/Bangkok"}, "departure_date_html": "2025-01-05 08:30:00", "arrival_date": {"date": "2025-01-05 09:40:00.000000", "timezone_type": 3, "timezone": "Asia/Bangkok"}, "arrival_date_html": "2025-01-05 09:40:00", "departure_time": "Sun Jan 05 08:30 AM", "departure_time_html": "Sun, 05 Jan 2025", "departure_hour": "08:30", "departure_dm": "05 Jan", "departure_format_html": "05/01/2025", "arrival_time": "Sun Jan 05 09:40 AM", "arrival_hour": "09:40", "arrival_dm": "05 Jan", "arrival_format_html": "05/01/2025", "image_url": "", "travelClass": []}], "price": "5,290.75฿", "adult_price": 695.5, "adult_price_html": "695.50฿", "total_adult_price": "3,477.50฿", "total_child_price": "0.00฿", "total_infant_price": "0.00฿", "adult_count": 5, "child_count": 0, "infant_count": 0, "priceIncludesTax": false, "discount": false, "departureDate": "2025-01-05", "base_price": "650.00฿", "can_book": true, "from_api": true, "enDelayTime": "1h 10m", "duration": 70, "enRouteHours": "01h 10m", "enRouteDays": "", "flight_seat": null, "band": "VZ", "channel_provider": "AMELIA", "city_pair": "BKK-UTH", "transit": "direct", "airline": "VZ", "timeOfDay": "morning", "earliestDeparture": "2025-01-05 08:30", "latestArrivalLocal": "2025-01-05 09:40", "booking_key": "e8AyoXwHrBvBdrcTFL5Yry7uFASfvXlB4rJluYd58k9GSAPoEGKlfXVu7a¥KQaKfMItkIq82xEjTm9EYvyloNOP9KNPnaR8Bap92yyrhLgYE8SJQ6nvZibDi9RfeFUP56AP6fRƒJReN7o2A837GQMNZs4wI7RcXcCHBpex5Ar9LkP¥yXt3Y4RhqZkrKaooFGZv1nWGGJbnJ¥KnS4j1Hv3zApDƒEh3K2WsYNHƒqEtqIFKxXCBzbhqqmlTNy6lq9XVGb9mmUpIZJYDmw9Uop1ZVzf6bWWb2wd2mwr5ƒh1¥U8NX3ehuqƒobL4wxjmzpYFKbEm4LkfMƒXPu¥wxI1OY58z3NjYƒLkkarFOJjMuhERƒsou8P¥tt4s6I0DFW7VtkC¥e7DR3vEaod6aZ9fsLRSxKJQna1QeTcl¥JIn57i92gKWTSD243Vb86wvVHxmkQXASkMapmiVNijx5xtrbPR4hJgnZMPiqHucVTvyYFlKM¥hRSGQ9Thbmbf4ƒttYXLH3UZdQT8ƒwlPGmJJyZuiNckCOeƒ5BmP0XFur8CgbnvCVmxDQ=", "fare_type": {"bookingKey": "e8AyoXwHrBvBdrcTFL5Yry7uFASfvXlB4rJluYd58k9GSAPoEGKlfXVu7a¥KQaKfMItkIq82xEjTm9EYvyloNOP9KNPnaR8Bap92yyrhLgYE8SJQ6nvZibDi9RfeFUP56AP6fRƒJReN7o2A837GQMNZs4wI7RcXcCHBpex5Ar9LkP¥yXt3Y4RhqZkrKaooFGZv1nWGGJbnJ¥KnS4j1Hv3zApDƒEh3K2WsYNHƒqEtqIFKxXCBzbhqqmlTNy6lq9XVGb9mmUpIZJYDmw9Uop1ZVzf6bWWb2wd2mwr5ƒh1¥U8NX3ehuqƒobL4wxjmzpYFKbEm4LkfMƒXPu¥wxI1OY58z3NjYƒLkkarFOJjMuhERƒsou8P¥tt4s6I0DFW7VtkC¥e7DR3vEaod6aZ9fsLRSxKJQna1QeTcl¥JIn57i92gKWTSD243Vb86wvVHxmkQXASkMapmiVNijx5xtrbPR4hJgnZMPiqHucVTvyYFlKM¥hRSGQ9Thbmbf4ƒttYXLH3UZdQT8ƒwlPGmJJyZuiNckCOeƒ5BmP0XFur8CgbnvCVmxDQ=", "totalFare": 5290.75, "totalDiscount": 0, "allFareWithoutBag": 0, "totalAdultFare": 3477.5, "totalChildFare": 0, "totalInfantFare": 0, "adultCount": 5, "childCount": 0, "infantCount": 0, "priceIncludesTax": false, "basePrice": 650, "adultFare": 695.5, "adult_price": 695.5, "currency_code": "THB", "childFare": 0, "infantFare": 0, "tax": 303.6, "name": "Eco", "code": "Eco", "max_passengers": 18, "price_detail": {"fare_tax": 227.5, "tax_fee_tax": 76.1, "addon_tax": 0, "fare_base": 3250, "tax_fee_base": 1737.15, "addon_base": 0, "fare_discount": 0, "tax_fee_discount": 0, "addon_discount": 0, "fare_total": 3477.5, "tax_fee_total": 1813.25, "addon_total": 0, "tax_fee": {"AI": {"amount": 650, "baseAmount": 650, "taxAmount": 0, "discountAmount": 0, "count": 5, "name": "Airport Improvement Fund", "currency": "THB"}, "AM": {"amount": 823.9, "baseAmount": 770, "taxAmount": 53.9, "discountAmount": 0, "count": 5, "name": "<PERSON><PERSON>", "currency": "THB"}, "HA": {"amount": 339.35, "baseAmount": 317.15, "taxAmount": 22.200000000000003, "discountAmount": 0, "count": 5, "name": "Handling Fee", "currency": "THB"}}, "fare": {"adult": {"amount": 3477.5, "baseAmount": 3250, "taxAmount": 227.5, "discountAmount": 0, "count": 5, "name": "Fare", "currency": "THB"}}}, "price_display_select_fare": "1,058.15", "totalBeforeDiscount": "5,290.75", "availability": 5}}, "return": [], "departureClass": "Eco", "returnClass": "", "departureAncillary": {"Baggage": [{"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghpGt5d2C3hlcDJA97rQd3p3lxv1JsERUChOhgWwSzoMOVSfPZ2oƒfApƒ741axxobO6L9fI6H2G2MXfHmfWoAcw7uTKiD¥v32lI5bPe7eoz3xCleOSzo9tOKKG3f9PYVKSGXAgRVG9p1TawvQjwKi7ucUZSMPwrg2RIeB67l0CzJS4xuPc0Zh2f9vXHVXha6gWwySZDFf6ƒFfemSKEIQMc65", "name": "Bag 15kgs (VZ)", "key": "eU9Fu4zikmtsn4VBXmWLNDuRTYtgfVylYHDErzHa¥Ns=", "description": "Baggage 15kgs (VZ)", "totalAmount": 379.85, "currencyCode": "THB", "taxAmount": 24.85, "baseAmount": 355, "discountAmount": 0, "categoryName": "Baggage", "allowMultipleItems": false, "maximumPerPassengerCount": 1, "free_item": false}, {"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghrnOjOEJucrmiQkqURsJ9y0JmqtL3s6ihzel1GRD0¥x0jAcZLz¥LLmRƒz2q9ƒmzErV1NyZvoFb5Oho4n34V2xP0b6nIc9dJR5F16b5849MDEd9BHhU4eZVasX1qJcU991TY6Y¥04WmdecZOgWSpYx8ESjgSebraJKyiygKHOpqKj1eJ3SVyy¥BU4Mj1OUlUQ6V5vDN1F92XI0fwL4xEYEBu", "name": "Bag 20kgs (VZ)", "key": "eU9Fu4zikmtsn4VBXmWLNJHV6J¥bX8KƒIiAOSIcIsA0=", "description": "Baggage 20kgs (VZ)", "totalAmount": 422.65, "currencyCode": "THB", "taxAmount": 27.65, "baseAmount": 395, "discountAmount": 0, "categoryName": "Baggage", "allowMultipleItems": false, "maximumPerPassengerCount": 1, "free_item": false}, {"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghohIIejhzJGs¥YMXmJiOzRvrfciYRZSYAJwlv2Bxn4r4Giols¥TdmEn4C4bzPC70TACAadFiPBCo7wRlO4L8OpyQPF5uƒdWR7LHeBBLuktGJmWFdV0e2ezRCYUfJFnJuEyvoMLvAGLZ5pR¥sUBx5v6Px1NAeTXqyjLUj4g5RAwo5hA1RqikNwioCcasSZhƒƒFxR2328r0Pcghk4IOCiT8lO", "name": "Bag 25kgs (VZ)", "key": "eU9Fu4zikmtsn4VBXmWLNO45NHGGqmUxWypmVYoR6aU=", "description": "Baggage 25kgs (VZ)", "totalAmount": 529.65, "currencyCode": "THB", "taxAmount": 34.65, "baseAmount": 495, "discountAmount": 0, "categoryName": "Baggage", "allowMultipleItems": false, "maximumPerPassengerCount": 1, "free_item": false}, {"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghq0SuS3AhiM1tzmqlQMvHITFxUlsfDDjp9R6RMADRnpVVkrzhoSqtKISN3vwoG2NW06kwND9AYJ9kYubCyN1rwmdtQ1dsplrgy¥SzBgbQL9hnwCpLRsVj9MeIwTjx2rxb2E¥3f3EKKw5dy4kchEyRQSY¥YfgvxFGBq8pvZe8AGVhH4d6B3uiWEhwUhmFk08dTBU¥A5dRurnjE¥lJICFLoWf", "name": "Bag 30kgs (VZ)", "key": "eU9Fu4zikmtsn4VBXmWLNHEqYF4nTL1CwuBLyzwAƒj4=", "description": "Baggage 30kgs (VZ)", "totalAmount": 850.65, "currencyCode": "THB", "taxAmount": 55.65, "baseAmount": 795, "discountAmount": 0, "categoryName": "Baggage", "allowMultipleItems": false, "maximumPerPassengerCount": 1, "free_item": false}, {"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghqwTFLUmXZLWSweCcyvIoYeTqhx7SMxFrOx1rkCƒoGmo29mAFc5aDXuwq8CVAskEIiz9972qoNGZTToMn9BagT81aUcSg61jojHjqnhfYjFKqTN2rDTWTmZw7XzZQw¥UXePATu1ƒViz9jrycabJIZp4mC35eaEt8QvmyI9rwZlXhu5FQDFxuXgƒzR71VEIGNvNaKƒ43tymbJ1¥mls53jlX9", "name": "Bag 35kgs (VZ)", "key": "eU9Fu4zikmtsn4VBXmWLNFyRtE¥WIh¥6p1z90SscGtA=", "description": "Baggage 35kgs (VZ)", "totalAmount": 1021.85, "currencyCode": "THB", "taxAmount": 66.85, "baseAmount": 955, "discountAmount": 0, "categoryName": "Baggage", "allowMultipleItems": false, "maximumPerPassengerCount": 1, "free_item": false}, {"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghrQOƒDƒ3DXVz4k75gMW9X6btLtW4ƒjjcQhnmndiqGsHkjwkSjt7EpSH¥QƒHYI2mƒJZ8JCUayT¥1MW6flmlqVaN¥VF6mGXfzaio3znkƒSuEzd0cYJqR891s9ƒGw7wƒYwcJbƒacfxTs4BaMWCrwypFRpkYghsNc9x8KitLYSOf9YfGxu82RYqWyFIeQCzPIifJwe2snmuCV4OƒR3CR32BJxyV", "name": "Bag 40kgs (VZ)", "key": "eU9Fu4zikmtsn4VBXmWLNCEuN6wwnE26o0twt9QSVZE=", "description": "Baggage 40kgs (VZ)", "totalAmount": 1278.65, "currencyCode": "THB", "taxAmount": 83.65, "baseAmount": 1195, "discountAmount": 0, "categoryName": "Baggage", "allowMultipleItems": false, "maximumPerPassengerCount": 1, "free_item": false}, {"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghrbnhWYe61rhOHƒ6VAyNW0m4SEY60lK9zpy3¥nSUynC85¥DiAOgr4SnUTLjkqMxjTTZZMgƒkfUszIMt7UBEhj5jNnBƒLlnuiNrysDg¥XJ964UOwmYeƒƒ5tM2fgPO7UYvHcPAe9dkSUwb2EKaoteE5hkblnmsmmrrBYDJ9JQX9Wuj2jƒJNCvJqs8zeXferpNye8uGUXtq9m04lMrtsBoUnWc", "name": "VZOversize20kg", "key": "eU9Fu4zikmtsn4VBXmWLNJQUIcuuamƒakVv1M3rdƒSY=", "description": "VZ Oversize Dom 20 kgs", "totalAmount": 850.65, "currencyCode": "THB", "taxAmount": 55.65, "baseAmount": 795, "discountAmount": 0, "categoryName": "Baggage", "allowMultipleItems": false, "maximumPerPassengerCount": 1, "free_item": false}, {"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghqx52Ry1nQzmdkr50lhhoFLSRm2ƒuou2G6Ure72LnYbdISN1yYUk1ILOHZ0TMHvMƒhS83j63KcJ¥xdmeDErD3Wo542DƒYHADZ0o8KmVkxRQIiI¥n7PE3hYAU3AQGisQxd1sqc69kR8lP9LYcdobkqmEIVqNY9QjO9dRVXWt8we8Fc¥4ƒe¥6gRdTWdƒIYepƒWacS¥37B1vmFSvJKeWxfewZy", "name": "VZOversize30kg", "key": "eU9Fu4zikmtsn4VBXmWLNJ71KTjRzx3p9uWoeR76hdI=", "description": "VZ Oversize Dom 30 kgs", "totalAmount": 1278.65, "currencyCode": "THB", "taxAmount": 83.65, "baseAmount": 1195, "discountAmount": 0, "categoryName": "Baggage", "allowMultipleItems": false, "maximumPerPassengerCount": 1, "free_item": false}], "Cabin Baggage": [{"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghpBiEGt0n0EDX1ghDnjmhlJCQBlbuGlHUibZDoQ¥M2bfG90KLyE1x3pyqxn30USIB204¥¥iy02aaeLNoSxKloAYuE4z9i2opp6MuGFqDs5Y¥p8bƒdeQfdp7Ivpo0MqlgUhMjcWBAaw8zƒqatyƒm95ƒzkƒF88YGu¥JyuEzeIIRTRRl10WI7an9uhƒOmMSmktwadQH2QulƒSOdVuFH74kgƒ2B", "name": "VZExtraDOM", "key": "eU9Fu4zikmtsn4VBXmWLNOX4EhrlDsEBiQ0xugbc4qg=", "description": "Extra Carry-on 3KG DOM", "totalAmount": 160.5, "currencyCode": "THB", "taxAmount": 10.5, "baseAmount": 150, "discountAmount": 0, "categoryName": "<PERSON><PERSON><PERSON>", "allowMultipleItems": false, "maximumPerPassengerCount": 1, "free_item": false}], "Meal": [{"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghojOSfYCN4unHlsU8yqAVjDNQ¥x0m0V3bAxnJkGycVaUwf27M3oVxeQQCDGzv3u8BgKbHXeAMQlXXMo6KWHƒYC28P0ke6¥iemaEkZjMo3OSgaTcXJLIE¥x4sTkMRj5dGUAyH43UEjdeNPAH7OySpFFyETRe¥dMVRH1hTStZEfdXwpn¥3fCpMaƒucgCCOWL¥02685uFT5q43ed¥KFp6v7PIc", "name": "ChicknTeriyaki W", "key": "eU9Fu4zikmtsn4VBXmWLNIAdPs¥46uva70ZhN1OpDdU=", "description": "Combo chicken Te<PERSON>ki with rice and water", "totalAmount": 160.5, "currencyCode": "THB", "taxAmount": 10.5, "baseAmount": 150, "discountAmount": 0, "categoryName": "<PERSON><PERSON>", "allowMultipleItems": true, "maximumPerPassengerCount": null, "free_item": false}, {"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghpMlxfsdKIQGwzCƒbxqqcCDeT7Pyr¥DlC2QƒLhbOGMf5OIr¥o¥oFN6eUiZWWE2¥ojbt06hNMTdj3Frlu0cchisO2zMPwHiBdEidvaYaMd9hkQxfWQR36aGWU5bqUNfOx34rf6E2p¥eZMPWuCKGmcuLbWGnOcl¥581rS6ƒ¥0gFX31c4V8ZCouM1YYpmfbZ0bhYB5cufb84CcuwPƒSaCmdG2R", "name": "GreenCurryCkn W", "key": "eU9Fu4zikmtsn4VBXmWLNKNLqvKqekt93sVtSv7T5oU=", "description": "Combo chicken green curry with rice and water", "totalAmount": 160.5, "currencyCode": "THB", "taxAmount": 10.5, "baseAmount": 150, "discountAmount": 0, "categoryName": "<PERSON><PERSON>", "allowMultipleItems": true, "maximumPerPassengerCount": null, "free_item": false}, {"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghpQ2UyLZ6NOYvuFGIaeYxuZiMtƒ9L75hC4SZ¥ƒPkQC4S7XpT13gLgkbv14WxiJLIofh7FcFFE8lAkcPuxonCWB2rbSyOuWtzVYNha0FmBManmGƒoA30cYGK6RLLEMsmTU7ffTISzWX7h0Ur7uHl8HC8jkn¥JVD0J0hcswo5TqAyYVphXflO¥eC2pqjhVKrm51yO1p5mo2A8yjmLdEXebqLc", "name": "VegetarianRice W", "key": "eU9Fu4zikmtsn4VBXmWLNPm6kdokhlI1lBgZo7NbVIA=", "description": "Vegetarian rice and water", "totalAmount": 160.5, "currencyCode": "THB", "taxAmount": 10.5, "baseAmount": 150, "discountAmount": 0, "categoryName": "<PERSON><PERSON>", "allowMultipleItems": true, "maximumPerPassengerCount": null, "free_item": false}, {"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghqIzdF8BxX0GyCmIlgZ3uNBEa5i9TErsdyTaaGDyNsRa0htmUgCwNjQVpNnƒr7rqWkrvKAer5E03ArcEqlRJaJ4waDjxUPltnnqnyFMxqWlj8MmBor10FViEhN292evcgUCwHtzDAtTƒ¥N07yusWjuOn7MWpI0q¥3HLMS14iXoMjq0VD5PBLlkFP5NV1B5lZi5JjvpsCTWVOc5yLo3Mƒ3ie", "name": "ChickenNoodle W", "key": "eU9Fu4zikmtsn4VBXmWLNBpq39mDn4USPCNt3z1B0lo=", "description": "Combo roasted chicken noodle and water", "totalAmount": 160.5, "currencyCode": "THB", "taxAmount": 10.5, "baseAmount": 150, "discountAmount": 0, "categoryName": "<PERSON><PERSON>", "allowMultipleItems": true, "maximumPerPassengerCount": null, "free_item": false}, {"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghpwTGbegVXTZS4onGƒ9U1XCam0ZrhDigJYSDn8qJWaqWTpMd7M1DZS0YI2Xqkp704yQTZ3TcC3Oyw9ƒbujaNCXN6pF7KCTvoƒYxP3ƒpM0p9ojzIvwLlIr1a3MdmNLkBfƒei6AKJQmjEb2hTcXzrWzfavYWuWUzOBk0Kdd7Vd8UXIp6A1JDIONkXhZj4702UcB2XSYLZQ6aHtT30oxpsEHNE", "name": "Croissantset1", "key": "eU9Fu4zikmtsn4VBXmWLNI7WbNDh6QFJrAFvFxDIXFI=", "description": "Combo Tuna Croissant with water or hot drink", "totalAmount": 139.1, "currencyCode": "THB", "taxAmount": 9.1, "baseAmount": 130, "discountAmount": 0, "categoryName": "<PERSON><PERSON>", "allowMultipleItems": true, "maximumPerPassengerCount": null, "free_item": false}, {"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghpwALHdr6ND7uiuPrYvnD9Hk¥lP6DzHj4HDkb0nqs48OfK0MRTgUJa7qyUNa2XID606pt6SN5BIfDTaM4xbf¥HNpvFI9DeuoWpCAH6OzIndcZ3cMk48wq9NmwD9aytTr9tHsolwpfYLjQnAJKmL2FZA16BD4BrlJuXQY7PSzjO9nosNl¥XsAuTƒM2jGC5D11umauQ02N2y77rkSAsKgB0Oq", "name": "Matcha latte", "key": "eU9Fu4zikmtsn4VBXmWLNMQ30VLKsrHegRp3jXPfgB0=", "description": "Matcha green tea with brown sugar", "totalAmount": 123.05, "currencyCode": "THB", "taxAmount": 8.05, "baseAmount": 115, "discountAmount": 0, "categoryName": "<PERSON><PERSON>", "allowMultipleItems": true, "maximumPerPassengerCount": null, "free_item": false}, {"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghqnrdqCyZzRCLi0TqXƒqf9hHrzMVeiFXb43JLo4Mm8mEmcLQPGaeXSdJUyo3B6SJM9y55Y8rJ9Y6TƒnCDnNZiZhU64K3RPhoRLaƒBarHmNLLaaƒh1GYNbChdulXR¥WYNoLkEgrHD55eu9J1TDXa5Zwe43780912FkGTwƒWVjfipRA5K6gIG3uASs0d4aTB1d¥71UqDVtU9DyJb3EaEaP¥6O", "name": "Basil<PERSON><PERSON>f W", "key": "eU9Fu4zikmtsn4VBXmWLNLWSƒ9mtIfd9uumyiinuTB8=", "description": "Combo Spicy Chicken Basil with rice and water", "totalAmount": 160.5, "currencyCode": "THB", "taxAmount": 10.5, "baseAmount": 150, "discountAmount": 0, "categoryName": "<PERSON><PERSON>", "allowMultipleItems": true, "maximumPerPassengerCount": null, "free_item": false}, {"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghpSkDDVUim2QfhyxWfuYSLQnf6lOnsjqIc1hW0V8VKeNEhGjJHWHy94D53Szhs6VNiFHwRmnefmqUQ4ZgOTfIjyIouWL5xZ8C0Imoƒl1clwTzErlY8nmui9tkSY¥MdZ00WM5¥2NWMk0nK9yZPJ6N049rwiKdYƒ5kAaNbmy8JcDyDPsHOlkEZm8UtydpfiiprkCM1sy3IGQezqXIfHhwmcFU", "name": "Iced <PERSON>o", "key": "eU9Fu4zikmtsn4VBXmWLNPFTXUWkJlKwEWENVjGlMOs=", "description": "Iced Orange Americano", "totalAmount": 123.05, "currencyCode": "THB", "taxAmount": 8.05, "baseAmount": 115, "discountAmount": 0, "categoryName": "<PERSON><PERSON>", "allowMultipleItems": true, "maximumPerPassengerCount": null, "free_item": false}, {"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghrcz9KeGV3ujGiCuWJ0HD61wƒMoKayu8IoIcbINxoi1Ra8tktJQJwEJziPUzlKFJ9yGDRmRN18LƒjpgRO0NCc3QSƒUbNcNSHalLHnDw346ym4ƒ37Y8Vk0tcXcp¥Bz0ia9gQZFkXF1ekFSbWH¥qOChSzGj¥XLEUqhZ2v2ELIbH7D3DycKVF1DEcqw6XmJHl2VE4QrIihA5ƒDwcR8v2HuYyUs", "name": "<PERSON> Bun set 2", "key": "eU9Fu4zikmtsn4VBXmWLNLHQD6xMvUWDd4LcDyUf0G8=", "description": "Combo Milk Bun and Mixed fruit tea", "totalAmount": 117.7, "currencyCode": "THB", "taxAmount": 7.7, "baseAmount": 110, "discountAmount": 0, "categoryName": "<PERSON><PERSON>", "allowMultipleItems": true, "maximumPerPassengerCount": null, "free_item": false}, {"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghpbyhYy5TkZ7VF9096HXaR4FAm4OYTMHKLlJ2bZaqLdFCx6l3hVRVMsnH0MelfCTsXW7vmnqEVVbdg5tEf0svtSoe7KRqRMgAecPOJfBEJzYfNA9DxHk1vEU4aPZD0Lm¥mUwywLA3jJFLqytLezF3p63wQ7uXNj3mƒJzfYwMyTOCMYwvL0¥kq7nAijc8VXssTB491N7RƒediweZrFaPSwYo", "name": "Cheesecake", "key": "eU9Fu4zikmtsn4VBXmWLNDSV4S2nTt0AtGHFj7HA4dc=", "description": "Souffle Cheesecake and Mixed fruit tea", "totalAmount": 117.7, "currencyCode": "THB", "taxAmount": 7.7, "baseAmount": 110, "discountAmount": 0, "categoryName": "<PERSON><PERSON>", "allowMultipleItems": true, "maximumPerPassengerCount": null, "free_item": false}, {"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghpZDzFTxdrXqlcFXBJ6EklsO53pg7DhqOe0wkxxuk1zFrYUJWiqOQ8r3LCa56T¥p1C1nrv2DVvLrnWTceEKnkWME8pVfngEGpF¥DGWGv4VoB0wpUN0JcCbJBqA0znxIq¥Mowre¥VmqOg8azEgBLI3TuJsS5nLEdim0BWKHvvjqwBpCpoC2UIyzMyIMqaxKvn6cjMjE37xWNy3F5ZogVvkLH", "name": "CrabStrick", "key": "eU9Fu4zikmtsn4VBXmWLNMxtƒ5eImMBBu2GSvKccCoM=", "description": "Combo Crab Strick Croissant and water or hot drink", "totalAmount": 139.1, "currencyCode": "THB", "taxAmount": 9.1, "baseAmount": 130, "discountAmount": 0, "categoryName": "<PERSON><PERSON>", "allowMultipleItems": true, "maximumPerPassengerCount": null, "free_item": false}, {"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghqYK4ƒFQ¥BotkaVoVoKWYX8cLOzfN6c9nIM9kUV5G7tfhI¥w8CjQGyoIsvjzrk8s8sBGvGnKNoFvvenPpawIifbUXbOExnXVtMlp8668j1uegnqVtOouA1WRXFWHoB5pMHRxq1easQIMjnMHDSEwV4ChUvpC3v90XpTSlodPZcp5aONLrVIgObXfOo9jnJgNKEo1AJyz3nZYNRb0baAEeA5", "name": "TruffleMushroom", "key": "eU9Fu4zikmtsn4VBXmWLNNsiS2RS4ZDMBijcIq¥pPjs=", "description": "Combo Truffle Mushroom Roll and water or hot drink", "totalAmount": 139.1, "currencyCode": "THB", "taxAmount": 9.1, "baseAmount": 130, "discountAmount": 0, "categoryName": "<PERSON><PERSON>", "allowMultipleItems": true, "maximumPerPassengerCount": null, "free_item": false}, {"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghok3eDMWKN7LRLYfƒteAnUmTgaOXBƒ7YlgC97nZuysC23koadVefyI6cOel6PbxGJvlNoiXf3uscd¥BvYr8MfZAGHF2aURZ4¥Esc3dytKleph5CIZAcmDUVPqsj4Vh5t4RNd7lwJdbnD4qƒT3N9zX1HOu9eeZjkƒ3i1pmJ4LQJOPZoS9uTRVkkj0NdI7BifB¥3tFOOZmlwLK¥2VPSpBukjb", "name": "StirFriedNoodles", "key": "eU9Fu4zikmtsn4VBXmWLNM3mPaLRP2JKG4dRm1rEVu4=", "description": "Combo Stir Fried Flat Noodles with Black Soy Sauce and Chicken and water", "totalAmount": 160.5, "currencyCode": "THB", "taxAmount": 10.5, "baseAmount": 150, "discountAmount": 0, "categoryName": "<PERSON><PERSON>", "allowMultipleItems": true, "maximumPerPassengerCount": null, "free_item": false}, {"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghptVGTpsdTB7y6ELƒkTm62JRL6izCcaJMuk2Hr3cR5JSƒBƒpiXkz1PtHX3d¥ƒYo4miyNo778wa9TPAG0xXrpKpRsNeW1FzCjwKHEgcW9777cKI9AZFHRTDRqxsk1M3FERczhbeF9JcIkrK7VG7lcJaDpsPuV4TJRHH6jNQhPagH¥jsjW4CylF2Ie5ovd3Niƒ12kuosYH5GzPBfXd¥pzb5eT", "name": "NutellaBun", "key": "eU9Fu4zikmtsn4VBXmWLNNsW3kjlqH¥prTFslOtJKMs=", "description": "Combo Nutella bun and mixed fruit tea", "totalAmount": 117.7, "currencyCode": "THB", "taxAmount": 7.7, "baseAmount": 110, "discountAmount": 0, "categoryName": "<PERSON><PERSON>", "allowMultipleItems": true, "maximumPerPassengerCount": null, "free_item": false}, {"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghouVPrDJeGLAGgTRnRYudo9NdiJieJjVkwp8aalokjkbaa1UKEQf43Fi9EP4KkPUKtP0YF1KNl6cmdWƒeZ4YhrM98S5uzv¥IKCkZ5E6jHBgmQgVscnFccV3zYuocU6BTZlVNsc7LCpKBZARF9MxtYwCRnsƒvrwlqW¥ibOvOhtB5dt2auQrzztv0OiDw¥P¥VIBcƒfZZFwdmxbje3KW06PPtJ", "name": "WhiteSauce", "key": "eU9Fu4zikmtsn4VBXmWLNOg7bpTe8c¥132MslFkgXsk=", "description": "Combo White Sauce Penne and water", "totalAmount": 160.5, "currencyCode": "THB", "taxAmount": 10.5, "baseAmount": 150, "discountAmount": 0, "categoryName": "<PERSON><PERSON>", "allowMultipleItems": true, "maximumPerPassengerCount": null, "free_item": false}, {"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghqKzs7iwWTƒUoTSTlEimMOHWj222REcHE2N¥nGu7xCYkIlft0guwmsXjU2epEqmasshHiLU5hOSEV7IC8WJCjGwUKTDbgƒGRO¥ItZVfie1YFJaHeOG9hD4tiEd1jvcOWQLgsm6Ghb3WcF3VuTuf8lxCzPWLXD5MUwmH9MGeG97¥mwNsqkv0pZB59CFV77kxI9L3wZ0rSPU6z4D¥n¥AqXJv1", "name": "ChkBasilMaca", "key": "eU9Fu4zikmtsn4VBXmWLNLwEvpePHWsyYQYmuIV0Zb0=", "description": "Combo Spicy Chicken Basil with rice and water and macadamia 30g", "totalAmount": 246.1, "currencyCode": "THB", "taxAmount": 16.1, "baseAmount": 230, "discountAmount": 0, "categoryName": "<PERSON><PERSON>", "allowMultipleItems": true, "maximumPerPassengerCount": null, "free_item": false}], "Priority": [{"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghpT9Xb6GvrjPy6BKCrj30BW5i544PcqFizemcdmujeQygrbLen55irBpOH0IzM4oxqeGC1ƒLi6bkGzaZiQR6bDHjMve9TOlWuzbRLM7rFyiw61KGD20AbX66160Zg6NLQv¥jHppogucDsognuBPHx20zP¥OtgbsgNoYlcGEBqƒJ8hswYBB19m6ZoUfUaeRoqiƒRh9r8IjhZTFrbQCOfR4YN", "name": "Priority Checkin", "key": "eU9Fu4zikmtsn4VBXmWLNBQVLaACxKCozbgGXYPP2cg=", "description": "VZ Priority Checkin", "totalAmount": 149.8, "currencyCode": "THB", "taxAmount": 9.8, "baseAmount": 140, "discountAmount": 0, "categoryName": "Priority", "allowMultipleItems": true, "maximumPerPassengerCount": 1, "free_item": false}], "Vip Lounge": [{"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghrdRFDJcƒzcxqR0yXI3CGhHPfYKBQGgo0M4EChnWPRKb9TlFFIPP1fgv8qTydyMuU244kJoL6YZafAQ3u5tY5JNƒm8waItXDALwn8UpuEFOWqUiaI8kSzPIvp3RnyvVGHbkUL7PWa86cFzLmY9bIFB75hA05LOzkndƒTrasDjƒXGzX0jsMEGoyytwoeo¥jNnr4KcJsfco6SQ0EGHyFaezwM", "name": "LOUNGE DOM OUT", "key": "eU9Fu4zikmtsn4VBXmWLNCTImbP68MnvofkUscbVSjY=", "description": "LOUNGE DOM OUTBOUND", "totalAmount": 952.3, "currencyCode": "THB", "taxAmount": 62.3, "baseAmount": 890, "discountAmount": 0, "categoryName": "Vip Lounge", "allowMultipleItems": true, "maximumPerPassengerCount": 1, "free_item": false}], "Special Service": [{"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghqMO8xs4qV9oraChv9¥XP8hU9c1W0QZhxucyƒE7XtX3zyElJyPaƒPjzfmM71ƒ4rJSW1cuBqOFHSusƒLuvJADc0TpesEnWTzDAahb6qTp8bRYEwuC0lvHƒAVS3KSkwZF64W5qVm3cnZnU21PGVJnNO7Kmz737vzbczcRC09CPYxJb¥lHhlGfdsnOPGJZnkvquZe9FRGOg2DB9KDsf4JkI54U", "name": "VZWheelchairRamp", "key": "eU9Fu4zikmtsn4VBXmWLNL0iEkJ0ymeN4RoswEq4¥OM=", "description": "Unable to walk long distance/elderly", "totalAmount": 0, "currencyCode": "THB", "taxAmount": 0, "baseAmount": 0, "discountAmount": 0, "categoryName": "Special Service", "allowMultipleItems": false, "maximumPerPassengerCount": 1, "free_item": false}, {"purchaseKey": "yq2CZ1dVlybyxQETDp1D5X94m3Iu0¥ƒIdNy3tSRzghoEeRHƒPPX1TvMkDmbdHsIKƒ7LQehf0RFlmkmF3U9TaQFq0ET8gMeA2syZ2hzOY8kxvoOJ6HLXjMw6J8bo3g9PkBxvqk7fwwGbAv6PHVCPVAfFmcDH1B1SH2CAwm3hObnQ4tLob8R¥myZii3ZIyKx47DlbE1kƒzt9rarlzpyy4i0e¥F8Uƒm6Js12ƒPerkj1dOlq2QMQeCwxvfypUdgCGENw", "name": "VZWheelchairStep", "key": "eU9Fu4zikmtsn4VBXmWLNBODOrJDB3HPkwbrkCvePLM=", "description": "Unable to ascend/descend aircraft steps", "totalAmount": 0, "currencyCode": "THB", "taxAmount": 0, "baseAmount": 0, "discountAmount": 0, "categoryName": "Special Service", "allowMultipleItems": false, "maximumPerPassengerCount": 1, "free_item": false}], "Skyboss": [], "Special change": [], "Deluxe ancillary": []}, "returnAncillary": [], "departureSeatSelection": {"1": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeYkZ3hOUNAyt9DUWydg1oEexn9ƒHAIDdRLKS5ZoCME4p0HzAB2A8B7Iƒud5UrK3lGhu¥UElV¥XFPVksxSb6pWeUzemifggoquxpgSGcr8cxWgjL8NR8hjcdUdJXFvGwr1A==", "selectionValidity": {"available": false, "reserved": false, "reservedWithInfant": false, "saleBlock": true, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": {"blockType": {"emergencyExit": false, "cityPair": false, "bookingCode": true, "flight": false, "tail": false}, "allowOverride": false, "reason": "Unavailable due to <PERSON><PERSON>"}}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeSwYLe8kMgƒ8EUEStvs54yODSsRJQiFpjVxRr4qAX2iDN¥ugr50ksIjahb2fYXU99zKhIN0Hr0QXFYzycfpeRA¥xPVYbou84vQƒCdomigJd1InC9w5jIUsWNejRAiLJWQw==", "selectionValidity": {"available": false, "reserved": false, "reservedWithInfant": false, "saleBlock": true, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": {"blockType": {"emergencyExit": false, "cityPair": false, "bookingCode": true, "flight": false, "tail": false}, "allowOverride": false, "reason": "Unavailable due to <PERSON><PERSON>"}}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeZyt45XH7AmmlLxgJXZNƒBwjBjeC¥haq6gSJUmGpWEfdoYPIJplZIuZ4ske9PtMMXxx5slGZ2XrPvJ32OfaYMDwdZcFJf¥a3JCt7oxslEgaMWSjDHRQdeRx2Ffhis9vUzQ==", "selectionValidity": {"available": false, "reserved": false, "reservedWithInfant": false, "saleBlock": true, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": {"blockType": {"emergencyExit": false, "cityPair": false, "bookingCode": true, "flight": false, "tail": false}, "allowOverride": false, "reason": "Unavailable due to <PERSON><PERSON>"}}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeRrvHb6Te4gwlRq45vfEBoFCbnz6iOFsxdYCY6qapVtpKP4huXN2IryuOhtbmWhrZ¥6NsIQrF83kFOSLrbwBQeFQPPIORuƒƒvuXSSjie7xin5lPbBL8q5ZCPa5OuEm05yw==", "selectionValidity": {"available": false, "reserved": false, "reservedWithInfant": false, "saleBlock": true, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": {"blockType": {"emergencyExit": false, "cityPair": false, "bookingCode": true, "flight": false, "tail": false}, "allowOverride": false, "reason": "Unavailable due to <PERSON><PERSON>"}}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeWtEpNJPƒVlwrN1cfw¥Wl5VdxRwl4nueacFsJWx2iIZw5bzh3Lƒu1ScRtqdXbkYd4iVIvkƒ9QjA7GHQeg3iaYRbHUJrzLkLZhVYhlyVM1VidzM0zs9rALƒQqUbzxjlP96A==", "selectionValidity": {"available": false, "reserved": false, "reservedWithInfant": false, "saleBlock": true, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": {"blockType": {"emergencyExit": false, "cityPair": false, "bookingCode": true, "flight": false, "tail": false}, "allowOverride": false, "reason": "Unavailable due to <PERSON><PERSON>"}}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeacsNQWrvyye0qFOfGGHr2mqaiW9Dcƒ6XhUbxRb48Vj6AgeKHj9uwMepbHcBP4gCA¥5AcIWlaz1KV6RƒvtEZJFZIkq0¥259Gvpe6TwrUpU9oTl284tBBy70s4SG5gD8tYw==", "selectionValidity": {"available": false, "reserved": false, "reservedWithInfant": false, "saleBlock": true, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": {"blockType": {"emergencyExit": false, "cityPair": false, "bookingCode": true, "flight": false, "tail": false}, "allowOverride": false, "reason": "Unavailable due to <PERSON><PERSON>"}}}, "2": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeZCZ3cwksƒkGFN3NVrkdrlkKL7VCUNEgGoUmEcCUicOzSoq4hAEQ5VN4lSqq7Qx8azbBHDYƒXlFi1GypRcXdmfK95VkJtsOlRBIƒTMGvsE5vokzr¥MB0HoMUt3YnmvGEPQ==", "selectionValidity": {"available": false, "reserved": false, "reservedWithInfant": false, "saleBlock": true, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": {"blockType": {"emergencyExit": false, "cityPair": false, "bookingCode": true, "flight": false, "tail": false}, "allowOverride": false, "reason": "Unavailable due to <PERSON><PERSON>"}}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeeGZVs0dqMaab1PlOuo2zoEq1hJsMznEwJJAuƒokKcUNwa1iYUqPHO99ZZHwi2Hvtkibf5Gtij6J9nqEuW3cKDdoC9cdKT42GQ0wkuwLaZfIi85rjJOArTd8gbSMgOVkQg==", "selectionValidity": {"available": false, "reserved": false, "reservedWithInfant": false, "saleBlock": true, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": {"blockType": {"emergencyExit": false, "cityPair": false, "bookingCode": true, "flight": false, "tail": false}, "allowOverride": false, "reason": "Unavailable due to <PERSON><PERSON>"}}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeahyMIk4eWeZa¥ipLN2KFzUfsƒKD8eFlJnxPKkW9tjc7MDwj5zviulRWMRFPBbGVF9tP89HeYrV9uTdvaE7mGyeHpg8gJBMAk2umH7cI8q0LOEjdqwpEMJqpFhecO¥njnA==", "selectionValidity": {"available": false, "reserved": false, "reservedWithInfant": false, "saleBlock": true, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": {"blockType": {"emergencyExit": false, "cityPair": false, "bookingCode": true, "flight": false, "tail": false}, "allowOverride": false, "reason": "Unavailable due to <PERSON><PERSON>"}}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeSkYpIVDgYxbEbjpaa9IzuPu92iJeoYWrhxkOT1LVPKnSCokcQ7wZLecy4F8DUnacYuQYuttRBzzuhgRa0W8lYoF8RPSoxFRuM2A8YCTZ7cKI9ƒABzFhekfdDD1yfI3u9g==", "selectionValidity": {"available": false, "reserved": false, "reservedWithInfant": false, "saleBlock": true, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": {"blockType": {"emergencyExit": false, "cityPair": false, "bookingCode": true, "flight": false, "tail": false}, "allowOverride": false, "reason": "Unavailable due to <PERSON><PERSON>"}}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeXrV8HS8r9GOKpzn16TvGscJw80pz1Wde¥¥KTnNVO3yGe3ZzARA94X¥iPW9EYv1GHclEYppc¥AU4m1lfJxRAmcU¥CT8f2N2Wqv21QzwDqhlP3FtVVan7v5Aqc2D2SAoHmw==", "selectionValidity": {"available": false, "reserved": false, "reservedWithInfant": false, "saleBlock": true, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": {"blockType": {"emergencyExit": false, "cityPair": false, "bookingCode": true, "flight": false, "tail": false}, "allowOverride": false, "reason": "Unavailable due to <PERSON><PERSON>"}}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeb¥RmOSf4dV6uptG¥Nx8rFt¥WpYxH2ZmQvP7mqVtnonoOE0IuWƒKYyebyxt5XbCom3RnFY5nU0v4FdYzpl4lUl1flejCcHI0OLFrƒoewRIz2wVm2AaJin6¥ZNEno53RO7Q==", "selectionValidity": {"available": false, "reserved": false, "reservedWithInfant": false, "saleBlock": true, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": {"blockType": {"emergencyExit": false, "cityPair": false, "bookingCode": true, "flight": false, "tail": false}, "allowOverride": false, "reason": "Unavailable due to <PERSON><PERSON>"}}}, "3": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeVn0XznlU9cxDyT9W¥xqT2NnHX2mPG6FkqZtMq2CVAhƒRKLruC27PPGBƒrKBTlcSHQexXlkCwJ42ts5f1RH7dAbk0KzE4CppYYE69s5bKfy2t1nzRtvPOQm4Dw9Ki0PMbg==", "selectionValidity": {"available": false, "reserved": false, "reservedWithInfant": false, "saleBlock": true, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": {"blockType": {"emergencyExit": false, "cityPair": false, "bookingCode": true, "flight": false, "tail": false}, "allowOverride": false, "reason": "Unavailable due to <PERSON><PERSON>"}}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeanUgksPIKWCsGzRKw¥BKYqyuPfQaƒyUB8C7eqpVDE8¥NT5DS3Kftbgt77SXHLYjhƒieqqguB2nBVa6et82LJGdMqsSz5r9K0SOVq2U5JgAMoWuBZYNMT1scbvj4qjgqmQ==", "selectionValidity": {"available": false, "reserved": false, "reservedWithInfant": false, "saleBlock": true, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": {"blockType": {"emergencyExit": false, "cityPair": false, "bookingCode": true, "flight": false, "tail": false}, "allowOverride": false, "reason": "Unavailable due to <PERSON><PERSON>"}}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSee¥ci5Yrh1pCM8A6LDILQXRrPrP5RT7KZqwKnWMA1vjJP6qƒrhZOidmDFu6jy88fKndTeDC6FgX2BCeBx00zxr0peKgOf2WhZv7lRXM7ITFdezCufETg1GLDnP6z1uDGDw==", "selectionValidity": {"available": false, "reserved": false, "reservedWithInfant": false, "saleBlock": true, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": {"blockType": {"emergencyExit": false, "cityPair": false, "bookingCode": true, "flight": false, "tail": false}, "allowOverride": false, "reason": "Unavailable due to <PERSON><PERSON>"}}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSebFSKoA6fD¥VyCtwGlJ947UvuOq8LFZJRgPROgQiE1AdvzghuIWpn75axk2ve2JFkvCBeD6S8zyz0bgwcFTA0h7Bv70vSFKsfSLfOv1ZAEnsxouR5tQa¥ZHfƒphgƒyLMVQ==", "selectionValidity": {"available": false, "reserved": false, "reservedWithInfant": false, "saleBlock": true, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": {"blockType": {"emergencyExit": false, "cityPair": false, "bookingCode": true, "flight": false, "tail": false}, "allowOverride": false, "reason": "Unavailable due to <PERSON><PERSON>"}}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeSegb4sx0oFMkhdƒFqMRP9iRQD3sfHƒtA2Mxi8i26KHIZX25KO3hTQavgV45BWkH1jBPjU0rZNEp5dGWk7X2J8ƒOdBLRlxmSMe74qJGuGRemaYge18lfAqHzCa6m7nRK6g==", "selectionValidity": {"available": false, "reserved": false, "reservedWithInfant": false, "saleBlock": true, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": {"blockType": {"emergencyExit": false, "cityPair": false, "bookingCode": true, "flight": false, "tail": false}, "allowOverride": false, "reason": "Unavailable due to <PERSON><PERSON>"}}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeWdBhFNaYn7XWXXPbAJt¥COeSZJlS5CdoeW9XhbYL5nrdtJjPUHPrnvVS3J1GSD2PoMIiL7s0ghRAHHq2us8Ozt¥5wFurrFVoejL4Zc9Cbv1mc6WCvQ0YkZCg7juXCDbhg==", "selectionValidity": {"available": false, "reserved": false, "reservedWithInfant": false, "saleBlock": true, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": {"blockType": {"emergencyExit": false, "cityPair": false, "bookingCode": true, "flight": false, "tail": false}, "allowOverride": false, "reason": "Unavailable due to <PERSON><PERSON>"}}}, "4": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSebMA76dZEuiQFBLTvy90s¥Q2yuouEhflELVW¥QU4w0nXaIY2IBJJwqPUtYa1OFwX0EKY1eZdfadyFƒznU6pjfsHWt3ValGGPg55y2kdOAvjGMPHlKpBIfstW9rEawdv2eg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeZaQgZUWvVvwDBHRvh2hEqoHwESosDKD35LZdzcv9bDrj¥gSh7BHhTJBRGMP0L0¥Tj2ge¥RF7iasXZDCgpcƒWrdLkubb8ZrST78AKYTpS0X6Al6zGe7haIsDX8JPI2dMJw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeWLFfHsE3Yja9WRNZZAjmGfhHmTkzVsgUfMCnrR6JEGLJYufF¥3Y1w1YXJFX6SvledDQPk7lf7RsƒuzƒYAhAV6HerPGdtlMrDDvYEdLyLRrPLdBwnZGxTYzH5T2G4rd2Iw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeYcqEEJDA8x4Gƒ5MJWoHAxSe3Olr1N4Q3Ttgƒy3MJBs2Okz1Tƒo4ceolM6XInXKEWBT3af173ƒA1dBIeE¥GwVzlfjEzyXSzwmJTqiE2FJTa1VF7Gsƒ7u¥8MIZ4BkEA7Lgg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeVwPwq9H3M7B8zWSXBzHMKSOTHoxBFFWfAVfOOPehrxROL8pmo¥3qpciQBdvKFrUZm84jdYJiFmt1lJWhPFZVMEXnNvZCSAz2KrWqg7tr0Q86Lav9WMmLC4BVƒƒq1b¥fPw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSedXmEXdJMT4srkUb7bejETƒRDSizA1ZiGG0¥U7KwdjLc5XQPJiV7N6WQZ2WKSXtK3f2xTIrRnxBcKweib6c¥jabTW5M¥TkfWnVAudazSsƒrFnC8qwRYsRvƒgWWE5S8FZhg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "5": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeZa3j6wKxƒ3TiSELYpEDovJYUhPZz¥x6OS0bVlAITg1PKXo5cx8L4sl8WPvJNX10nAFn4o93jQzhOZr1JchzkmƒwDVMbco3bcPoLH2CIE8W3qhrYC9CLFNRVn4dgvrsbvg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSebYjX6gPhQpxy9KIvoEalo1bgP6XT9gFEQV¥TrQYnBAV¥lkmwsqXlMqmOd5¥YnBgDirzkOp6IsYU¥g4SaVsFJz5GbwhDTXPKGL5zlL7DbeoWrAiYo61IaJBOAN4PAt6ECw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeRoDSgKh1xU9mYF5umlK0UMy¥z8bcalb9Olq8alMVCQƒfOwBo3vdt9EOZHCtHdNM4O8xA3TrDx5ECs7WOJcpIvpLskgsYQlnNXFMRcWhBGIEEtJ7QntCSgeE5vFHMoJDzQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeZnQUXNyXcJbRYSzixtk6g¥p9l4nskpaEXwt0Vetƒ8V0lXBlq24LOD¥tNik5kQHx¥psF¥Jie3ƒPlvA0ax0Xbe¥vVzoG0INKF9dTiFGjBvne1NUNn0mbgX10OcpDWYtBT7w==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeWj¥cvƒƒM7EƒGrMbT2vjƒyeXpKqKdjgdZTsK2z8LUFvfCD0HrT5h¥ccFnyok7qcL7C8VaJJQFlx8EXG0IOfsDm3EKjIjQBzqgRSnZQkdoxX7xCyieY2p0owCau¥70HP1mg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeSlBzƒc0adWGƒ5kXqlnZeOagSr7T¥wv7E6pCMxQcrd7d2¥SWJZ9suJL22yMX2aBTZQF1eqODQoYfVINbKh1LWLarLV6rFWrLHn¥apl9EmFedO¥2ml3ssk¥2y47pB9Yijpw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": true, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "6": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeTtDAvTnlEGsbf¥2eUbzzND6XB9mY6bZf8VMrMg2XMTKL0zZ9KZzJvM4dGBaRG9sYcƒtxtFNU4wMXk6dmQyYRIƒNyD2KSSkViRx5N02eOIFlƒhJPhkTAVj7tsVSNoPde9A==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeVVVqXclYZvuWMmnN1XB¥ULtzVZHY6igEdcTiwdehAFNDEyicpvKZLF8N4sY7jRGZsIiXWAUi2CNkh5ICvFEbY2BYUkVeAd9CuOhXzKm4bHl6nOW1TLFxh5gAfRRQ33jPA==", "selectionValidity": {"available": false, "reserved": true, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeQ0VUj6ZsZ2U7WNWmiPhq3wJKRWRHP¥Y4bSalNlKeJuGChkrBgxjNDnFRLiFfkDbxXqvgNqLHXhpDrIQSHHrkOMSQ2E49EaGhodWDWjd¥K7wzB79bphaxjnMB3TQPxQiaA==", "selectionValidity": {"available": false, "reserved": true, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSefAVBSVhjuxtm0gkSM338E9gu0djweae4uA5uuXXE9KXjTbgETUqZƒlSvxdIbsynH0jt7Gkssd¥UQeg8AD9cYppZK5hSmzAHraw2gI0V9M33IxoQpgTF8ƒ9apWpJ6qUMyg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeYiJWbƒIcgeIvi0o2nhd1QItUJNhkrBLZ1I6vHwCkJU9jXdLP¥oDs7Id8nl4A6ckpKO2hgA34FBo6X1pu65Wr1a3pjeibƒ3PzAynAGzaujgtAInZMxmqbWYhiaJD6IJq3g==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeYGFB7qqfKses34eVyy8treK5a4KuYP5RM5iRYuP8grAqPUMaKPs¥jk8gsXDAup94PYycjrb62K6KcQ1ffƒC07dlwlmyw5dNMOKc7bi5nhw0vgAEXAso9As6pNFfalBRyQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "7": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeb6qNzTVToM46SLWpN6nQu5¥f0HV31Ar9Dc3yAj1zSE2nl44HYwdrU2K7q16lTCqkY5mGi4NPB45wNTVyOopHWKkMhbrniem4vXU86MwjkYUKwFPB¥vw8em8ty9GhhiXFQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeSdrBl6wCTn4kBz3l8Y2¥MyG7UwN9exeKZSjƒYVK9vpRrdkeX0AitlsnƒXw7WtTvMS8Ij1OA35PbF19dAu2qTxRVGkIMM4IXQjPumƒdlOtvvI0nzFƒ7bUQQ6qXznFvbySw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeeMCLRfQS7FYCAL1Mjhaa8uHKqbsoTJnU4oSkcnF¥y0cDR6h99RUQKFyLJ5ƒi1IGaV¥AcPX06kZyv9pRdDnSdwKoZtm2RD0POyjB5cpsbThDx1NyTyHzAq7XVzuz7I6sbQ==", "selectionValidity": {"available": false, "reserved": true, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeWF2P¥buMh5I0QRif2OEBT1dR4vl5hEr¥qOvPiVDƒz9cbMJ2jJVWRQxz1Xe6hVdufBFXX1k¥GX7ƒtHZVwH9wQHbGv6uEOd2CRP4W73vcmLaO11ZsjdKKPSXEHir8QejS9w==", "selectionValidity": {"available": false, "reserved": true, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSebdEInK12wGwO7IXxPTjKw39njTmAFaAzid5kWFhB7ƒnnjCZxOYQIBvDH6lOl83VYv¥bw64pUvqLX6VCjQXAUFEUGiWCm8vZZ¥eEf8Fiicoƒiq9ws6KxnXƒkfXGbGWM8ZQ==", "selectionValidity": {"available": false, "reserved": true, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSecL0iDxƒe0eiJQ3Em9cAD2ioVa1OnvpH6tzuUymBWHspLFq5Aq6v696vVMGPCygKgUKmNEsbBfQtn1fihjm33zYQVh07wiQ2tuvHZ5PiHY3iSMwETQ78S4zNr8z8ƒrwSzg==", "selectionValidity": {"available": false, "reserved": true, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "8": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSedCExCwƒLhfKpxUƒƒhMllWi0SpZjFtsbHqv8PDfyzT92OJGFo1jy2w8LHpmCQqgLiGFo6dFqj7OdHvƒqimYW¥WK5yJfUbprInYlv4Um4nQswG4NisAaqgwuqSr35xaal3g==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeSXfZ443oyIqXRekjbƒp6QQ1vP3ZyGM4e69do2M5ODU1QAZZXNNM065xbjC4gi3RRpB7jChfuRBXAPSGsedwdKNdd5Jyi4O3X9RzES6McvExmRV5¥UqBMUTPfWCCPZ3bfQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSefnGVaxphw¥dgkzQgfKi86asa5n1pKcpHWB092gE4v7nzBLzIPOIfLFZWmzqEKClbxqsCY4HpGPPVckI8s1dpjRjc6yM7rT1cvPsZyIY8c9V1F1vD¥RbtUSkeO3wnMcAHQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeZQ15LyhoBN18BllwGxi0ZyFoyEJTLz05HyqMkRngRH1YsPPJb1TGUehwCAp2ZtTgvDisjWPUC91l7Fqx6kjVN1kh4pRJzK8¥R1Vekn0CX4h8ntHncVSsDJp44LnBakdoQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeb82gc5bmYwrj1923PeKvCSTwhjYV7BZ5BItLbmqzW0ShmC8BC2CY9KAeF2z0V11Nb57qC45ttJZUmD¥ui5nkJ7¥x4t1p3zvrdzewHbcJnIVQ¥HgknC3QtqZvbn3XsvoIg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSedOJ8TaNRV6wpWwrCnqZWkIƒvC5GHQov0Wn82l0aM113wjxEPOAz4iFIi0P12DReTYu8lPSyq0eDGMVsUZRwqvNhƒsWxMs0qSTI6GOqHGQdX0gIf1K26YƒffkbCJOepPdg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "9": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeXP6uD3BvOHcHethPat39qz1uKwVNPXFf2f2h30cHG¥6N7NFvZcrAeQQpHWLhohEU5V7ysvWgWnwsaGOUvxZrotqxbƒbU¥OWmbIABsU3gx5Db4TrirE1jpxIzrsUrk3T5g==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeS0wbqJg6uoUgjQLpbDJ28sswQDf7ViNRhwfTgdoONt8MUaTW69I4Dbvwe1ZGpKWzeS64gTtMMXPIkP9oXZNv1wTGSfB5¥Pem4rwrPctƒnN1z3Irn8eaXB3hgVdL8mR2Lw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeYTZ8Oxg6sCmƒLj0S63wW4qOƒPDyMFCvtlmjfFf9eQ3xk7DJup¥KvzZTNrbEkNuN0ozZmyw¥2XxJ9EdavzuZzKsIBp6rfCb6ƒLTTiTg0AymytWXpLv1W2MZ1NlALrnouTQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeQLfD37YuJ4wWuADGW1H9JDPjwdh5VKUVX6b2LdPCyKuUkYBOTlqjkOe6cRSZRz4sbB3jqkgJ0AO8gUR7iNw2cpZZPsd3xi18fr86iFU44uNVVAxzixgZ9EJYPi5xƒstLQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeaI8U0v3qacguDSmg9Jc¥A8¥nTNy4w7Y¥ymcb5oymV4idhZeNYn6E1aNjv2S4vj4C8puvwErjcCm8frQa4Y5jb19HXkwMtEzux1H5Imf¥ff0tKaKjdKJJ9Hhx5nJ4ƒqJ1A==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeXERBO0MJMiKSqL2NVC1mdK4L7lƒ7cMzMOCiTmqPNSYnWy1s5¥pGZROwsbbo5Y1¥qvt5¥uAsLwkHwEF1x1zP6ƒjVb6UHitXWDQojSM3O7Bfta3uotfGEj8RgFGVb8NmJKw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "10": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeZcBr¥nxeqO8ZZis6ogJ6EdtWEejjug0Le8SfxmcXuBKvQPdmNjQHJmfBuiCG0Z2Dƒ0eB96LdbVJEyP3LI56zJ8Gbr1cOuƒ4LOZim9hd9pdSSZxsRccUWuQzcD¥aMiDwxA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeRyeƒM6reosIgHkS6MwJ29MlmJzQCCwVG2s4Ecpiv7DWYm1vci5KULE1ObpGD5i2FCSyrgHnOzia7lZ3bnnj63HRxlkPNAZfP4t5cqDBIUhnmFmVrDoipee9yNt4MhaZmg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSefS5ivPtWrkƒJsp¥TihArg7dNULi7ISDu1jLnJanFSy7vNil9YYQ7VqB5J6iP6qkH1A5Hk9t1cUC7SUjBf3bu8jZAdazhQRvdG8LMJPbkdlPMVOu9QAu6zbvCsvJs14p6Q==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeZUNTK8ns9CW3vu0fupde43nFjwEHEƒXM9ipjXQpIH1l9LyNkTcdku1Il3OoƒyU6ocjKbsj0ƒƒEGHY¥N1anK8xcN1rIVpd2dRE5VWdrwXIƒ0FnmJR2rdol4fM6zHBbDVZg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeV1iOwTOtz8maQ2GyBLuZPFL1h7B6ynfIHQH5XLVNeYPTSwSKsI8WeAS4vBe50¥PnQd1zExHƒJe7JftvPkaA7GXoc4psRRX8azrR8FDw03WNSxknMC3mq68x7srW63JJ1Q==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSef1vƒw¥XWBzhiILGGmQckEOihajRwƒATvlxyFiYsSSdLSSz5YO3pW14¥msfcIdEeHV97mƒ3CctsQv3Oa7R8by4872¥XƒHZFkXxugJFRpCbcY7nhrXrXrG8qQET3pFnA¥8w==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "11": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeUHaIhY32OeAu1ilQhUB9LzOzpbC3vThFozt8Vnju3kS¥5W5wJi9PZbe0fgmZ7ew¥9YVjD3P2WTuDmXƒs27E9VHUbGr7C30vsHd9eqtnoƒVkvHpZ5fTb2ZZzsK0zoXsmTw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeeeyI7LcLPL7uPX9cLctG37x0VuiRzxStaa¥RmƒFv0¥KoN8KSHaulHUWB0K5vc3HAEPxj5D9xS9d75eBKSYWKiFfO1ƒWu9XuG2JHJw3HjvSvVujeJCWcSnTDBYgjbB2NIg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeSouHh4w6z5fk5DQ1rsntpYbp5DTYt3VfjcpnnrrD2IMUƒzWZV4L¥tDCBnFtDG54cD8Rm5rs7MtCacrgcUP47lnROh¥GDcHSapde46eENCD8u5ZCb2FIcƒ2pX86NFNiECw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeT3XV7ƒcmVoCo749jwF072MmN5LƒOcgZKv3mEOZlEyPm2v6sLZgzumjj46n7R9hVvaRjG3anpb0jpci¥jt0u3qIfuNL¥TiwhZhJOtzi90vzaGqQQkk4wsT5ƒbQGQq9Lpmg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeRtB06BPfmyYEEZnRRBd2ƒ¥V3k59rMZafKWgdXZFPC10d9T5E3ZBIDr8Z2Br7BARCnobq9GEj6ykA5mdIwokenm2eh6sy6wnIFgZugiiApsEqƒ1pYMKtCTL5qQelHwuƒqQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeTPQP9po6gXBHSpzpGxjnMpirfpsi8WllgDx9qcjhjikYrWuiGkL1lM8VXQcqmIXbaoIf4eqƒreUG9Rw2ZqYwbƒfcby0h7kxiiwukjvMFrjbbjHCNwjEpe9Yy3rXWhGSJg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 120, "discountAmount": 0, "taxAmount": 8.4, "taxRateAmounts": [{"name": "Tax 1", "amount": 8.4}], "totalAmount": 128.4, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": true, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "12": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSefwTQgVh2EpqP0hbaE5pS02EbMRdƒv9Dwk9YpUiRGW6pGIKVL¥CTxX0hd4lXW93luMkeYaEClUEFZ9A0UCCy0sXrDAdfQNUPfdUeSGZBCqCVp0iTqTz5N¥AwRr9ƒWOCZgQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": true, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": true, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeegYvuh6A0ESo1q1uH¥sjwPnRA7E6zFnoG5x66XwƒX1t2gNfrdJv23DcLHJwrBJEkdAL5nnOAlLeWxuaDIdS6DhQ4xMƒj6s9bWlWICaNImM4y5FZ0qSwG8chC32mjcplFQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": true, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": true, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSec3E6Hp7sodeX2vyuzF5Rsfg4UJ10Cno¥9ssdc7ZeQKdqVbG8i8zƒmvujƒHdgT2jxGEf2uRDZOhwz1jPk93dM488z02Ntbsrnyhm9vXbbhMJlNa446npGyNBiƒjyO¥2ApQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": true, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": true, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeeek9c3HVMYwKOnwyGSKLm32K4gR3roYoke4gjxuvz6hVjC6LDbKC0MmWJdQym3ctH0j1pphJE23¥BGwƒNTtM7PgvAL5qC07wzouJ299CWyMo3x6AiEXr28fWTwcyCMzJA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": true, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": true, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeVnsYhyFP8cU6ƒZAacb0LExe3MaHqƒymKpg9RB7Gsa5jS5EjQmlNWQeF6rWBwB7KkKcSkMZ8DxDWjZHeVeB0B2xr9HqZensD¥ƒRCfbUxeNuNGEw2JorymOUk1hn32hAbVw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": true, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": true, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeRsNhƒ9EcTfwFuilLfjviAaqo86kr7aQRZkGnWguvkxMbrAeH20sOGrvC4zVqNgOGZqCwhhhTtMEzkMU7oCd6OAZZpYljfjBhhUMFFuU¥0dgJƒUzi5bpZDXCxbtTSPO6HQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": true, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": true, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "14": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSefE5H2DTTfos04X0Zmco9o7jL3GOp6ySXI4Jd3AN¥ru6ƒuj2BF1YGga9h7cB¥vk9CF39gBYC3d1ChCpjP0Ho6fVaF7BrOmw9t5AwkckqEzJotrWndZOgPeJy3RXh7MboUg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": true, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": true, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeVNsMVcv9PgTiEKIp¥qQs¥y61P9f4qHeEku8¥8p4tA0p4r¥QzkzExotkM3ugVf5zsnsJW1cebbNlyTT59qDXrXsmz2Ki¥hrq1UQEGIl8sjBG¥RUQuF6UZ6Xar5ritVhuOg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": true, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": true, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeRZDfohkWxJ33hLgTo8DWMxV2GnuebXaV6oTyqBA2Kjt9ƒ0g¥RTGEm3qnXiap6BCoUZYnpwhjWVhlL5OqVvQr3OB1HgQ92nsmlHr3DFJ0tWlSNe37tEVHF4Kd¥HWGGEXƒQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": true, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": true, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeWrBq5r6l9OU20ysZ3kp0z5mDqwB6BjXVLGKIgqqF79HFLisq05PpBHizj0tlZXELbLY4CePpBHLWV8TjQ6O5SkbuVIK6AFM6AQ6vHBQA9Qo82FeuFBOuXukhAAAdCTbYQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": true, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": true, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeUtuJqXuYAr8nGVc5gsHQjbP¥tLKG9xrTT36a9rFSnZzbALzJKbT8izflQTR5YFT39MsFwVEFoPb3ƒqyO2vSiPMafy57ReH8giEayH4H9XOmc1dKjKxhVpCxqGOCƒ4KjUA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": true, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": true, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeS9YyuUR3P5Goini1x6MPi82HwWVsAVidgvVPZj6GMCSVHCf6vRjDR72Ru3uLbBAmsrXCKcqkFbUBBL2FMyssq6NATMvXEUPV1GS8LxiGHDaX4LRK6ndstyZiDgGyCeIUA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": true, "seatCharges": {"baseAmount": 250, "discountAmount": 0, "taxAmount": 17.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 17.5}], "totalAmount": 267.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": true, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "15": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeSFu7OhVRwWiJvz13MqrgFmkXZvB2LTOvzuaLbpOGofJPiIPTPSW19O5wYomxI09ƒlIhy2bpwEsoglG3NCocXsFDyMNT5jnulwm17vAdUQn2bPfouuDzWypkHfMI¥ibSJQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeUlv3p¥hzi¥anzzxfudWeKS52RVRnswLVtfmR4ƒ1ƒRhZkI0o5jfnmTTo10wVo1jaDI1CAmIr4pkL5¥LUzk7gwUhb6lZZmxSVzDmCXiUAPM1WSmxzZ8Smd1mVTZQA9fUOYQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeYU410TaMQ¥JW2EQnNlRsLIxqShBHFhpCQnZ67EZtLnRYKQ8MxYtU0o7NTTYNrG5RVddDctBsb3qFC94iG¥MflacFU34pDMpSdQ9ZvbxvjK0O2nEDKAyHIV7b5HAXƒwQZw==", "selectionValidity": {"available": false, "reserved": true, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": true}, "available": false, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeaiƒhfƒQXIWza41672jZds6cD5B0Z40uN6vVMtm5dWh0Z5vLnl1oPvAxtP7ifB6Y6D9om8YnrOrmHLzKsTx¥pOYQWiINTGxt081LE0oIxy5ls1oePQbskzgN9ƒlMBctG6w==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeVogƒnU1dkSHLYjWqeFQFeiqEE1sqdAevCIHlkXvpqJd¥xdJ¥bepfjSKu4sZ9ttFhWByu1L2sehkoq2JPD2GVNZO2gfaWkt5h1t¥lAoSXXKoP1CYƒQ2nFRlWRZDwwWKVzQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeaQWlj3pzH6kx57¥p9X6XYLse2xOOoYDn8N1YOZfr4cik1uLY2hpZ0APCRTIKSIev0S3VPv75qsU9ƒrE¥URAyw6LXBWzqj5OoqtxZqƒWprb30zƒEƒPBuS4ITOzIUTKfGuQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "16": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSedIgmWLx2ERQGoeQSRobFm¥ar9HY5uZRqfCpAX¥WIUvNBbzVhN4Y¥joQnJAwtRoo7b1rMfQ5ƒHUsiIIrurZYWsniwOtjsWjCyO80EohsRd4ƒO8XyNUccSNtRoWuGKm45Rg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeacV6dSFpphszfHiDaeFs6znbATIyCNBrwy34xlXnBWvIK33rkdYE5B¥s0LfNQT5UaNL5ipyBLLur00waOEZpsƒGX580Bg5RAEXƒ1ROW3YuRcXfnsYVxjrAxvcsgIFSDUA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeXDh1xfPyhot1A9xzLJAZ9UAjVjg¥Xk8298rJE7TLsuVxfVeaaYBtH¥hXkamP0BkaVjPuD2pHLSo76Jm¥wegSbJiPW90KS715IPkKPsUh6FbApGmn59eg¥Oo7gIcSoAƒƒg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeRHKzSsxGsEwA2XUvgJLBWSvlmEZhƒRQuh1ADflGV104AeErOjWfyzIaZWhF0cCNRtgNy0vN6bVJOiBBb3rPv1tsFrOqhU5kn9Hzotjrt¥Z0QQ0q0¥ƒhwfPwSI1WqPdpƒA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeW5W4Mxklr9¥CtpvLwZP3wS4uTg87ldauz8d5ojWzIN3¥yoPsK0aBvHbj5fr88zBLCTyAU3dm7cZcrOivUsCcxv0H0ZjPU9PfacxKƒWOMjicpI34jtVgFhfv5MEn0IDhWA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSefjUkgEbCTj1cn¥Ek7lLƒ8lC8OZKxa1s2lFJorOƒb6BI9IcTaEaIkuQ08YGm6¥EyGuErw17ttUIqVnQWKNGDu7UGBz8SDvNuc5To78qS7q2NCbCdVcJ4mgzOiYabCvGwsA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "17": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeZONGqgUHt5xqZ5RyUPRosht6mGHm26xYbZ9Sv78UC3rNjwzhGƒ9FQ0C1uow8f4c0¥t6XccPLTAWƒlgc4R9NcSVPƒhJbyM¥Zsi4rqE7okYoqFVwSWwzHXKywJxT2c1ƒqeg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeUzW3BH4jGMxDChQHxLAqjpJjJlIQ7gC5NBtcVyjvRWt03HNl6WmmsdVVUGFFmEc94enAxFi6Byl6KYGJVIHGt8CfI1NCTEAƒVrxmBRenGqflo1Q9usGU5KKRFOOd5rvXA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSecumVlq7PƒVGf0ƒdvLl7GzMyL8u4L5SPcYA4Cls¥Z91ahs8Fb8PEoHLPngFye1vCvx9uwjeNS9QWn¥lD6pXƒfgFHƒONs4ngqnf6q8p3rSya09fctSYDxqDeipuOoOdmbBQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeQJCEh4vL3opb3jMmjEYY9VTROEI2OEuHxHMqXEeMz0mstwhNIxM3bcszp75QAK2x4AGCWeO8wILGjJvaiDLZVKQwppCEL2MmpuScfO2rCpramED7cEuKC05XKEOdZjvSA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeY9ugQUJWeHjOx5OQ7U63iJyTikxaV8jpvSpZXBaK7jc2rpSjOwPx7drbwhAcCCdOVGReunmHJ¥dlƒW4kGm8¥niw1c65BTugBmq7jBQl71M6FADDemhy8h6ZjVOUƒM9saA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeWuPoxKfJXFbCxruyGK86OHZq0Ax6PuScnwNNTxlZQcTRYQZHEtWsRVHrRwF3ghDlUAEZ2lcjSeTSzx4fr3Gb5hR9SCBDuhlo4RRW2¥QWy8dxDtlsqwhKY0pwSafla5ZQw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "18": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeVR111bl8v6OvwqRXFbJPOlzvAJlJXidvX44b29q¥CƒvƒAlhfQEQ7EmwmQniKb1jKs7Ol¥EiEgjQoUtHHsYGAm9NcyoMo9T0WYXgbggƒi3uLGsTƒkCqvYJwR7i9ODsfXXw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeQhLYnGMrV7ZLxntpNAnkljƒ2olKhRPsOgUmumgIfoRehnLIVJpPLsZxpfy¥QcVLy3N5nunKltJQPJoMHMVghDkO¥ƒ2UIpi3tIKPQƒMj7b4teTieZTADEVoT29OWSu4AdA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeeDseyr2THjDECBO¥TRAurPyUZQpCakxx3m0z¥xfye7IxeAFfhNr2u1BeRƒ8pTDƒn1Q1jDBOnqsQX4yFnB5d0gTMRUGbQ6vQwZ766PHS633FZo71fAdJolQv4WZBEt6v4A==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeRTIB4hPEQs1Iq3Ckog3w2aghljADkz3zYZntBFQSTlYT6xAJgeo4npVƒvtE5fsnIQHLuWX8sMqrDVUJiPWQDKDXRGO1lJfRjzm7Qd6rWj33ƒh23IwMzQƒ16r2ƒf0kRvzw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeTAKJnƒREgpnupd0dgvnN9I¥orEpeƒQmB4Kb¥vhTyegqxa7ffVOktEFZBI2KiƒFAolkZYnivyBo¥1KQTliEjgZ1pWIRmq4wAh9LFKqzQFtyjuCtBDki7TtKVsy7D0KxwXw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeVrw4RD8llITtCHT9girPq3y61nDcg7znTsMInquƒnOWIyyHAl3Ey0F2RvN8OhZwFqFEQJKM42zDQV32gNOHHZgAEdtlPW9BUsOc6Bm8fXPRJZlAƒ0hZpDi61B9RhcoG5A==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "19": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeRYg2lpCp41MJnsXfAJXOT0XBBILWfMcrtBhTnPEvbAfUHGWƒZBuL9mDMGo0rsxlkb0sBphgjUiw7a3AoZVk1ohM7s5wTqZPmGdTDijeJU03DBhmG8lEfEHna3yekdh8Aw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSefZtV7tIV6hjD48w3HiƒX48cz6A8jbxOPGGvq2V¥Gƒ6DuNOascImVCXKf7pvdrowqnnWFRpBfMuP¥PXrrVd4lc2WDPEZU9Erh39ƒyA4sQ5aipNC¥lr9oC2GZVuIsN6fszA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeQknhtsEEswGxdi0brQvdjsOcDhCgxHB6Qi¥PLAdbughIpYj9kAO51qvvGaUU550w7q2R9dKUZjhv4efLqKCu9Y2pfdoZsyZakXIMjhEtqi6wWHrbrXUisUK1mWfUg1Cgw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeRKkmu9phdZziVly14x4PƒAePVCPV8vPhi¥wWIffyq6qHP7ic97uCVTunG0¥dPIL5PIUC6BHUeƒgECjY7tQqsBg6V62ylsOfLppGtt8RXZ8ƒYTIaukv4IVuD0oHuR3QiGg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeX¥zrRAJC4kOhcT¥qdTFVD7PiEV5tBkLKƒyL5AK96MZNYLSnCHRjmbRugJenbuG¥dVzgroWoesbIg8DCLQAbADp13qEZBr3aƒWahajfnruRksLƒq9PhK1F3KjCgGKinTLg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeTQF¥Jz0wf4kj5Z3PXW21lA8OBJahsƒnyVAQiyG3lWwo5EKhmnAƒDLVVom7t¥4SbpHsvmUC0QƒcufRde¥OGN7w2qZFlD1I2FTRAYrqkeewgbK92xnVJeRWBbkZWJzwDcXA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "20": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSedUZrjAwGYyeN73M5DYAƒmASgk8¥sc9ymfN3RTReT9kmA47ApM7AmlTERVxAkAngWSzdAƒAuPum2gRKohWXlgW1WwƒFUR3wHh8Mxiq¥b6UGRQLA7kGbGBEVHEqLBYFtCDQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeUoƒMJKECsWWLaS0zsUGUNKVB9ZEQ2hvEJ9t5G1EIlk2FjSMintWuwpfo1fIF0mTO9C95¥pwPGfEEjDFrDcbLnY9MxvU7AmezrRh1N6KQBdecwCp¥¥EFtB¥v8w4VrMfTFQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSefUmQBoCnJbXxF0VnEKknohVOX4xUqZ0mXXQ¥7Nc71OvfscFk3cccDuƒ1UuMVMvylXncPT8Q6J3zt3SH2i0I0pEyB¥eƒDvX3siNHXH6Bg4vSe0jWlUEygLhuW49H¥mKpYw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeU¥swHjcv9FfyjGPDVxmaOmvYoVl4ƒb0b325jg4wRuH7hhKEwPpGkIcJBNQFgEJNPCMOA9rr¥ShfTXEhX9zzCmdB8UmCTScPy6sXaPvlugNpNojxBbQLkAVv¥yGO¥ƒXxZg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSebVjnpdxeQ9ElhaJoPDjBmeOtbtGƒqvM6SD3q3¥NHl¥mXn7A829pxmIGSC6vgfwcwl4xe5sq2jgXƒE3EZwNY34tIZx8nMQ24YhKpcQpklmi¥v0r7tG2HXa9DUdfOb¥qLWw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeRnp6E¥g7HtPyrƒoyRszYHpƒqmQPLhwGDljW2LqIXxJ82YRTXH¥usueHnW6QZSaH3FSsl5rFNng1oRnp3yO2C5KcafhLCEoiXj7fGhcwPbbƒYMaKTrVHNofr7¥CIr3krLg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "21": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeacuupYnBB2FtTrDVVHBpa¥ST3qWQoabrsKmRjqQuCnQƒqNb7gg17fgp5ƒ91H7g4cCƒzKrOB9mq1nqZRHbKidW7jgUfNJJvƒiiHQ9p3A¥myaRz1vxnycZxrI9mKS¥3Iƒ6A==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeX78o6j8SBwPpMoLtYSNXtjjY3lWARLlJYoMhY44lzaƒ¥xƒAƒsx¥ƒD5fFo9VGwZM8dBsenuu6UXdJSLMD3f1vLQk2rcfgDucCyng6UzUSwOi6AKVtgtK80UxE7LDllPDCQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSecc5AuarZ9K0QtaWOgrtQG6ralRI0961lSAbro7kbUdL¥ZKoAFYhbLgBBEYmTiVItv1A6bCNUWQJwu8aCR7FGwCIƒL2ƒ0nRjIwfZPu19FJSdsCGfIlbT40e7s1pn8TscVQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeYVEl292crehq695PFzPdUUpKh1UUQLE5GpuGbVm6JsRƒBemubMrrUQoRX48¥rGgnl35hJAToU¥3uyubdiS14P¥1n359EG¥ZhVAqAEFsvc6ARmxFYiMTj17NMK2fvSkFeA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSebEUTT3H9xYƒUCnrXwDfd33v75bmlvT8LdWƒKOVLFad8gks¥0KUaQaRm¥Mf7uCugYEyVTVC9Mstw¥UBKHv1UpiTiJ5hRhjvOI5kQrdzMAm¥5NCOSqvLoiThSFpXEmr0QUQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeecdqhLbMfRl9911goSa7b1Za4SUCPGVg8EfKeQSzhyqZfOA4WuNTmgTLX2BLCHHqQ8UovShoNBpFOxfl3cJ2wRIUUcLjxiQWJOUbPZ58gR1M3eVuVnGRBW82AOXXeMm¥Q==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "22": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeSaTUTYZy128TNqvKMmcHqdjGns97uFzVzmMbiPTIlc0Gc9s2Mƒ2¥NGjgwOWicAEzJ1Dum4t0iRBfpYJUNFSQKRdWpqUh2gCAOrajSIcqiDjFLWqPƒfm10q3XhƒPVI9lnw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeeYqqyurJXgFqcPOlCKZDxcP5eMefx49w9MOOq6uIskkf2pKXK3quY1C8PpVdUZPaIeƒAM3sGW60XvDjevhlllD¥DujiVyl2anBoYMj1Hms8unS0SH¥EyVCAv86zSnxClw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeVkZexJG3k¥ASh3ySfS7LoK0n7lHFzuFmZbcn00nRQZCViigkY3jl18SgM4AGqVbtzF5yUdsQXjzCkDgkPucVFvA2dFMXRCxrdZ2TN7H80PQwZ4ek2Izo7yHZpqfL0ctCA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSebKpAL75ga1STmRSAJzRQCzh7FSnYeOseZvLTAkZeQapWUxRSc3iiqrFgRP0MUHezRYWDb8FcmpTƒVRmGiPdIdkvuƒWtgPEZksziAqIyuBLfcuF2JnOXkvojDIDHftymXA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeXGjEdhDGmwsRWaPyJfdtTqgM2ƒZSuGJ7PbJvwCExgVIQW8RN5oB5UjefgnƒQG2fWUorW9vfKc9VvGxVbEBwKvz2P07qCbdwsZUUFoZ¥Rme4b81sX4tJj0DBEGGUha74qw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeU7A5KedrdB2kXdpOf3XdoKS¥A2aJcKMmtQHvH1w9SWY0ƒctIRdagk7ncEkM0a2v5Zd8mkjN11571luQsFpvxVqNx6Qyv7w0lI72MmaxVLq54n9WVGv4hbO3DXA2c9xJƒQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "23": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeQCTRLfA7BChHER2EaƒqAI4KtwOH4fjem7oWPZ3sUiyYNƒY4WNR4HMƒ3VfxvB1j¥8lUrXJYaQSRƒfD8I54iCymtrh6Ba7aGBySmKvxjoƒWqNeF6rƒw8xcNsfoWHXxihf2g==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeese8B8LrwCyW6vCLeNdi¥3dk7h5¥xWIIIƒ0gp0¥rYnyI¥Yid3ZfEsm6JxBRuobUES0QCHEAYoz7fWrOGLgoGODTrlKioNJy0l1plk0Xhi6NkxAyV6IpcmmD9E5ZvP7MiA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeTKƒYqVejdUpmD¥xZNZ36YƒDGG5t8jBeyFI5cJLAeqLQt5iG¥U30G8cK4¥IS3dAZUHFsw3WMlzUr2qhpdƒChrczwIQbj4ZBtM53XRUfxHmOLGƒmdw¥BysOKqIEgKƒGe6hw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeUJf2qMNHLUvzsMsEfrJ51EqYMNj9t7cSqckut0ƒ0idkCLGzgoIwCzooPXGytTb0bIW1Ucox6K0Skns6O2iUcIKz0YwsnOAfKmGKTmƒj8Ja237CjEhkU¥ZYzpjWsxu8ViA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeQo5ztvNz3doCFƒhvphQBC2svUybA6v7rt5ryw3geakVFAaa6a6iUR5cY¥6urpTp¥cG0LFDoCQSehHhnZIe5Nnok97¥4ZqAwYPkHqVnYcKvRW7fTxYRp74FrYku0NJfY¥Q==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeUpk90eG5Oq3bjtkpb1G1ysMFSa1JQuyw2UOtvT4wAfLoGƒ3p9gkRtjgBDaS87u3Q45ZR4h0sAYKw23¥509QD20LPdXPQRvrpqU1r1LƒcfaYVdVChn7oyBOA¥MjzkMIXzw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "24": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeZSsSpQCdƒa¥tvmbXqaB8WAsHIbiPd6cHvuZAeZqftSxx¥AbqH66MzwBdfO11OBA0a4ƒJs2RKJzE9tK8vmerRsfTyEXzhw4pMXpLSILZseCgqhEpyN9nidBTcfuYt5mlYA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeVMHJpm¥sFL2l¥mG8FEauCSeSyGkWpNQ9SKtcfjWM8Yj0jFiqGO40H65NjJFFZPlBl14OdhIvxiGssx5Jj1VvbsLƒny0Uex6aBWyO2U5Gjg8GXr67CrCf2OXGn¥UI2RuWA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeRWsBM4IUxXmS9zhs114Tt04zkehhSiK3Amp3akR5hnUyfzAxS03iqCYNGMkJr9wpXU9euG0KvRGvQNTkqs8D38NUSSEmTyo¥u2PaƒHcaOa5ujjX¥CiddaCpTZ1BGX1brg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeR76UƒMW3575HNFm¥FLGAa0NfcYUA¥7T4uLnbYr0iHYq2tdaqgFRHq43OZ5B7V0vb3VXkma14¥WO¥tjNTfXTAhNCn9rlnryolLAf20GVX6NVN5KSxL4Zq71qx7dbJzdb8g==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeaPos9fbkGah2fso7tZQV8B0oJ09QgXhlWIG2A2Ru3¥juzRD5iBLTe2cJSvv1TrLsDoXznGQSmwxKvh6ASWIjbgF9UttR73xzqf3dQaAKllQ0by9r¥tscQxBDeFrxWkƒbA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSec56Qg2E91K66K0G5cT5KHsQQLzV4bOSJE3lv9CFqYJ9GDPP2dF6NLUFEtai0tIqDv08mB9yEE8C9ZzXtzs5q9otjWgh4l9ƒeMƒAYZGdqU5JVhFtJvS5vX7aTTWzurBLjw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "25": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeXhUREMeHSpAntqRƒILcF3bCq3JsƒzujygT0rXOAPRZƒlUl0nFJSy078w6Jr4ML1KXunAkYEbƒclUgaAOFbnxYƒepkxm¥mh6uTYlZHFOTckƒq9zIcmhKnlKa4utU26Lmgw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeV1kGghEE9OufCr¥cpTl6czLPQG67OnHLaHWihNoiyoP5GIrGWZ2FF7toSSUR7ODIL7Rh4iMKt9PKmNhuFcM5¥dbWGiOCLjumPvhfaGY26ureAHbzqteWUODgQdPFB85LA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSef4TRJdHcUry8ƒanlgRLlKJosGDoZ86S1JR8yIBZg17N0LuTFdHgNZxUx9JnLfGZkavt8WC4qATVFO9ƒi8qeXCBWv¥XGiaOqIkAYMzzid0Ah2toM¥gI1qVG18DenJSWk7Q==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSedf6fhAnkPDHXMZoMaIDqgskNyuEk9NviqtJRYX5XxafV3ƒnqPy3xCGeWfYXƒNHCLjKR0NW5iliNunSguiPgh6WaivtƒDKRƒmcHsFchH0zJEUZ67TAvBPIct9vGn9e26gQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeQacGSCAR¥o6vri61N6E6zF5e5EQQnbV9v50LtpqIbXTMQtY¥fJOtrrs24R6Sds4B67cJZzkxAƒIKC2pZu8Rxv7g9ZUFjzmwZgFx6MUWrxCvwRw5L52SLZd4WJ70gnG5fQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeWO¥AKRnnJTtbggc24oYNM4¥0XobfPp2kUUp6jOOsSRQ3ƒUcCY7lMLDpbaHoa3eJ1GbkiarJtQTry4gvjvuSLkFZNKOebSwofYwHavoKSsoru6nRZRxudXzzwe0ouCh6ag==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "26": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeeK6G8XU¥ylCD4nmqRwupguztxpdWNXYqSRwmLITrBgvcnya3jySMhZedMSfbV1FFYgqj1Ot3vmkS4vXeuNhESfh8hnyvO1dGIY882fKIgYcbRoViwszyƒqmUdec3DuqPg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeZIpktHLMA3vNdZQ48PiGWzWgcC35ESxx6sswgiƒRnJmƒDDsqawt2nXAk¥dXf6blETWRkDRYQvs0t7c6kGkumO2CFP6KOOajo15smdYXMp4zyupsZjowEpAzPZadEzƒFFw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeX1w1B2SuoMIoCbwBjeDoiwVUAXEbHHAp4U2hnzTXigEXHXNr6rj95t1fGxNnztydSgclpSkaiobhE3vwIc1Pf77jxElAy4InbTIuuoaedgPwdROsU¥m6NFQƒIX12USqPQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSebhidmElWHuMlh2siG9Rkil0Iw¥r5XYB6D7As8ƒYNQpZvddLadH1¥zcjrpRnAƒYzMKXsWMQE4SzI2m2b3ZswLho42xuKggB0G6WLz3uTl2ddTVKj6TcPKƒdfFqLY0kKzeA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSefjVF6XVCDbZGYkBEgMg3GIfu9HnZnj0cHVddPGYMcW0Beivcm3ge9hTSgSecaƒTsmlARqbDcqYP4QHcaBydUFGsToZ6hPWLOmGiLDB4L9¥Y3prIsdp8JrkJBxko2LecRg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeV5f3H9APVbZBVXU79j9WTzy6c3gƒtB6hIOorBYI2OHhnwnsBQAZ5YZbslpCooqBpfVTYDhbjozx7xCWMK049TAcym0DsXQ2lgqkVWit6XZoNVV¥ƒeXW6XoFsM5xPcKdCg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "27": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeUBDnn2KLB1YOS8UeMGHK7MsBpQ2vJwKCvlLNLYBpY29lGd4lIKUMaRjCYLECtbN5KE1aXƒveCos3cQ¥Tcb9AWLNBF7xUaaeGMdvMuLmnHipzYfkj4hkfO9QiObCk7I9Hw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSecSOpMeDgCogDk1BDK6fnYYOz06KlwWRXaYWƒm0lxh3ohRc¥d5IgOitIUF6mRjcNDenGbU39fOƒLSgKWITG6pa7h7IIOJTycj4ohyGrNEdh8YefWXeKPd7s¥1TL3UIeXeQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeSƒmbmƒqjmiI4hk¥VUREVy8D0xlTJmcC2TB2pnaTfpmvkqkCreHA3JzmsnuAelyRe1VNqAAViLQF5niKTs¥QfAOTLjZbOWW0wYdPFTkEIz2njjLCbGeNZYoQZ5sJr0UASg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSefO7VU4260IOhiPoCXChKxeSrQAk9d9heik77pEruU3Bmhv1pkVuw6Bl7Fƒj3FbWzF5CuSEN¥obNAn55xmkwtJE1a8cV1ZvzyAHBfmFUeIwcRlpDLKƒUWBhIpKC1ycCj3A==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeaZbIdXujuikZ1sI¥sUH8T4esj6MBeXdBUtahoUnRWx01QAQtsqj90JEƒQhqrjNzl4mERp6DƒIziXCObtjYnFR5E1XIlFHl75xf5uNODLgtbNKIkciXusYxHE0E5JTL5RA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeVel5ZrWvBrWbMPfMmlAƒFySmsgEN0ugwk8rfG90bcFKFL7zYWUU4dzRJa8qnyEByWjF45AjitfPSrnn3D0x¥nH3Q6RIXvyMEyƒ96ixf5tJAATxFKXPX39GfLZAmDVZPgg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "28": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeevSpo1Ws4oO5LRPUnfPVf4IAFLiBV3xZPqvG1m9639Tev0YTjuvCl7avPa4B15hH8rs8J3i0549KSXhOaZƒ2n1LIoƒzo1Qt0ZC00ZG5Mwf5NApU0DJ0jcyqMhƒEj9yCGw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeTlNK6xZwJ2RfF9yKABKWpk6qKXhIfKPrcE8¥mJVvl8uieQBhIz5r3wXw2nS4Q4OJMT1aRgN7kUQKNvliPq9eagwvyS9wdHG156p9sEpXYuA2g¥¥wlF67A7gvKcqT6etVA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeSNi9YV5mz325aadAv1xazR5wKaWCv6gmqQKLKgAqQCADM6PEyASpn88S6hB2h25Om5rTGƒbq7HtmI481qmtG4rNF9M7czaDgO5Er1OaAPƒpPxwxkIDEWUXhxvz5BbJSDg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeWPrM97eGzpzpKDRCwldjQSXnCNme7YXhƒFRIn9l84oGikela3mZuThicgD6xyMQ3uvdY7dSE7WT8CuYaH2Fepoj3P21bnjrUhFGoEbYh6ƒe7JhbKhZvzCFP84ZLMYdGmQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSedKZcxl5KMWe7GjVx8Yƒej20oEPORinzy0sZ1w2v7¥yCmRalgtUdGbTdyuPoJr2w0HvrRy5Y4qc9w8j8Kxspn7R5V6rsgAXVe8¥3L0SiR0MkahkXScHA6iujƒs5lTQN3tQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeSbƒsVSK3TBs7pwwa122PtKa7OSb2iMkAKK6XXqrzLWiZLoeJSmJGW0rI7HsAMfa32gC5F1ZxRƒlFtNuCM7umyR2N2Hu3t5ƒ5afprI7IFIo1yzl5aHOqwXJ2J7CrPm9Gtw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "29": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeeRarƒ9OGHZV4IzMLwNSƒUGvSGIuTvaNPQM2QU1YyY8f1AjmSVVvlZ8¥TO4exH94aƒcZkPsEz7qr7Hz4mCYC0¥EzJF2fKac5AJdaCHl0NmaTILZkffUpJgniDlbARdHutA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSedSamL5XFfF2rLylMLD1IƒgxYxBE06ILLJ1iskxk3kctO5FYgYSin09cIeihaJTc0Qj3jZ¥B6d3iVyƒD6j5xbwlOHtoL9X9eNCcPQEu2KDcxs2ƒEv7dmsPHO0df14QGv¥w==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeWGgJnZoQ2AFZNl5PsMRs5dgunJDHgxfhFARU5l9eEQ7IeccwKKfNVg4KbSAzR6UGmptbhog5g9Sg6Nbg7QTBN1oP8UCbaBPo8Q1FOK11vdJSmIOCSY7BJRXUY9szWY6VQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSee9iEi4JabuMH6XRwFq32jqeImmc¥JIxjdEXvRBSaMiIsBtEmRZaCRhqTEIElChƒKqƒW0kJ2rQrSdBANyfZxZv02muVKEcucm¥T5MZ0iPƒc2nMuSYL1QdOv2EcRxtkeu0g==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeeXb¥vslQME54oyKxvr7iiFEk6Nz963ROarnJGH9Jeƒn69¥2J2lvql13¥QV7jVuN4TtGYMFyFyXIAxxvZZooGHEVonHYƒaP24pYBhESQSMW0xvrMMkpjG¥eLqVW5g¥bfCQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeRiYi1ZkJJZhCX9o¥¥HusYxmQ75wYlL82vtTVTGvJJIisdBDXJiRLaERDaHPFU8GD6cU8ALwotWbWO1xAtG8NS9dze87GqV3eASUgJdktkWQƒMIPOsBLTeB08hg5JgwjUw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "30": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeZuzLMuT2QMkgmcGIe9uCamzUgeROpKVwCJ5jJ¥JQTLAkFehUDrfGVv8QlDcA9HKCL4lNpDL0ubAbj2wZfbBD4AKZ47SKƒBnhx2TSBTHSaQ8IMWƒeUAFƒ2UTrNjWC81nqQ==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeTWKi4varplHrjƒVROx7DTMrA6edpXAaGoYUgepTZjVMqZmN4aanXgh¥9bhgKN26R2Ro3ynnqZoa7zBU2n33Fq1RX2FVHXOQW8QhY6tXR2jCYPfIoeDIe5vWrIRLu¥arpA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeRVH0m3a6RJ3Fp56ZCzPdtIPSttnXAGjNwcYQ5LjhKl3PoxvBEp05tWvDqGr¥YWF43MNRyqGbWkfHDLO3JPty9mWmNq9X8cqEqeQtYxpQw0jECNJ¥NHSLGguNDv0lLfƒBA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeU71oexA0CPrOKSbjQ9gUWuob7EnPel8tHn5P5gTUwZzHbhSTADMi3AKYod9yOHbPEsƒgLN1npymMgkZwwfexJWoTcVfwFaxtDpjgh13qJudoO6HtG¥PpIdRc3AAKBKrIg==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeW3L5VIEtMv7CXu1pM6bgpBEeYKdeGJf2fOJFLIB6oMkI0¥XbMCXxhks3RHj9aaBF¥76rb03Pi¥GPudRdAzPxzGMwTeYwG2P63ruqrD0NuqJWnpqW57VkQaQcL24PSsy6g==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSebjBDmYErn2aLPPlƒzeQWtsYnKQirJiBg7DrI7lEbpEPQ5TF3xqp5EkpU5SNS3A0Gw8qwcdUE5W8YUCTRzVXU3VNERG3OHonvw2JCenGUREpP3sOo12Xt85UjxD4C¥0q3Q==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}, "31": {"A": {"rowId": "A", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSecHRmEgfdAX91uBt1dxSDQZRE1c6nx3RLFuNKhbr8PcIFclecyJAA9vtNq3hPMBklbN32C7k4WM1txofjq0FƒedFWAoVdUkWue6v¥x3zCWHJU6V3fhlcLDkQfz4VqHgPfw==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "B": {"rowId": "B", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeSBKbwjywUEqH¥O6vZ2C8DBfnui4C27MlYvIY5mwQnm71KltO5GzAYhymBwIgMx2kTbtGj2GBUYGpGXjeotJGIRyVZBSsQ0gO9Ud8bvƒƒUJs25cDKfZgXD36m5yODlXg4w==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "C": {"rowId": "C", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSea790ƒ7h44mZaIGGaCU7dt8NBTZKSyNLACSJX19qv¥OcrZDJdEJOXX¥tMT3LCUFQGmC00V3qgsoXgY2Hzd54sDmpZ9sZv2idC59qdsTszPzkkKf0Kgn31y¥AyNG9jIAh2g==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "D": {"rowId": "D", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeY2t1xBF39Uy8YrpCr45s0OxZfV137SxbSBv2eypIhSnQ12zbDNdVehDDaTdC3KOkGLUuXrs3Hy2UvzIx4VZmMESXL00Ux3HsYSFPAJx4Lb¥zaHwvtfC8V1T¥Ovƒr9mNng==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "E": {"rowId": "E", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSeapRtX507pdClwFReIjHm6Eiu7pFci33O8argwfxHyXBbkƒZcC8w6LA4k9oSY8ZPe1ksƒfZDS0nFI0eZUKKLAƒUuc9uWaCS7Mlg8uIzMNBGaKUƒWQH1jjmtIzXxCr1geTA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}, "F": {"rowId": "F", "selectionKey": "s4jEl0JQEs1wwcF7st62Y¥p6i5Q¥4N6bswPuTMcuiG6HgLp51ZC88naRhQaWv6xPCVGydkAƒOW6pI2GOgNmSefkSlvDkGG7HkƒHDpvwpTVZdPxKBTqGxrsGDt48vƒHdIVzvdGPumgcyDSC9uVGSl7s4vPcXy5Haoi0wpsIdiSHFmhVCuNM4aMCN7a6yD5F3brYdEK0gyyG2hRQVƒP4k1lA==", "selectionValidity": {"available": true, "reserved": false, "reservedWithInfant": false, "saleBlock": false, "serviceBlock": false, "invalidAllLegs": false}, "available": true, "emergencyExit": false, "seatCharges": {"baseAmount": 50, "discountAmount": 0, "taxAmount": 3.5, "taxRateAmounts": [{"name": "Tax 1", "amount": 3.5}], "totalAmount": 53.5, "currency": {"href": "https://vietjet-api.intelisys.ca/RESTv1/currencies/THB", "code": "THB", "description": "Thai Baht"}}, "seatQualifiers": {"aisle": false, "window": false, "emergencyExit": false, "bulkheadFront": false, "bulkheadBack": false, "overWing": false, "nearEngine": false, "limitedRecline": false, "disabled": false, "lastAssigned": false, "infant": false, "stretcher": false}, "blockInformation": null}}}, "departureFrontAndBackPosition": {"lastFront": 11, "firstBack": 15, "columns": ["A", "B", "C", "D", "E", "F"], "premiumRows": ["1", "2", "3", "4", "5"]}, "returnSeatSelection": [], "returnFrontAndBackPosition": [], "listAirLineInfo": [{"code": "VZ", "name": "Thai VietJet Air"}], "totalAmount": 5290.75, "baseAmount": 5290.75, "departureTotalFare": 5290.75, "returnTotalFare": 0, "flightServiceInfos": {"Special Service": {"code": "Special Service", "title": "Wheelchair Service", "content": "<div style=\"direction: ltr; font-family: A<PERSON><PERSON>, Aptos_EmbeddedFont, Aptos_MSFontService, Calibri, Helvetica, sans-serif; font-size: 12pt;\"><strong>WCHR</strong>&nbsp;: Passenger who require a wheelchair assistance between check-in and boarding , are able to ascend/descend steps unassisted and can make your own way within the aircraft cabin to/from your seat.&nbsp;</div>\r\n<div style=\"direction: ltr; font-family: Aptos, Aptos_EmbeddedFont, Aptos_MSFontService, Calibri, Helvetica, sans-serif; font-size: 12pt;\">&nbsp;</div>\r\n<div style=\"direction: ltr; font-family: Aptos, Aptos_EmbeddedFont, Aptos_MSFontService, Calibri, Helvetica, sans-serif; font-size: 12pt;\">\r\n<div style=\"box-sizing: border-box; color: #191d1b; background-color: #ffffff; direction: ltr; font-size: 12pt;\"><span style=\"box-sizing: border-box; font-weight: bolder;\">WCHS</span>: Passenger who require a wheelchair assistance between check-in and boarding and can make your own way within the aircraft cabin to/from your seat but difficulties to ascend or descend aircraft steps which need assisting up/down the aircraft stairs.</div>\r\n<div style=\"box-sizing: border-box; color: #191d1b; background-color: #ffffff; direction: ltr; font-size: 12pt;\">&nbsp;</div>\r\n<div style=\"box-sizing: border-box; color: #191d1b; background-color: #ffffff; direction: ltr; font-size: 12pt;\"><span style=\"box-sizing: border-box;\">Wheelchair in cabin (WCHC) for passenger who is completely immobile and requires wheelchair assistance to/from aircraft seat. This service is currently unavailable.</span></div>\r\n<div style=\"box-sizing: border-box; color: #191d1b; background-color: #ffffff; direction: ltr; font-size: 12pt;\">&nbsp;</div>\r\n<div style=\"box-sizing: border-box; color: #191d1b; background-color: #ffffff; caret-color: #000000; font-family: Helvetica; font-size: 12px; direction: ltr;\">\r\n<div class=\"x_x_x_elementToProof\" style=\"box-sizing: border-box;\"><span style=\"box-sizing: border-box; font-family: Aptos, Aptos_EmbeddedFont, Aptos_MSFontService, Calibri, Helvetica, sans-serif; font-size: 12pt;\">Note: The maximum number of wheelchair requests and other special services will be limited per flight.&nbsp;</span></div>\r\n<div class=\"x_x_x_elementToProof\" style=\"box-sizing: border-box;\"><span style=\"box-sizing: border-box; font-family: Aptos, Aptos_EmbeddedFont, Aptos_MSFontService, Calibri, Helvetica, sans-serif; font-size: 12pt;\">If a passenger has their own wheelchair with batteries or other mobility aids,&nbsp;</span></div>\r\n<div class=\"x_x_x_elementToProof\" style=\"box-sizing: border-box;\"><span style=\"box-sizing: border-box; font-family: Aptos, Aptos_EmbeddedFont, Aptos_MSFontService, Calibri, Helvetica, sans-serif; font-size: 12pt;\">please contact the Thai Vietjet Call Center at 02 089 1909, email<span class=\"Apple-converted-space\" style=\"box-sizing: border-box;\">&nbsp;</span><a style=\"box-sizing: border-box; color: blue; text-decoration-line: none; background-color: transparent;\" href=\"mailto:<EMAIL>\"><EMAIL></a>, or contact their Line official: @thaivietjet.</span></div>\r\n</div>\r\n</div>", "banner": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/rC5IWHdGM5O4O9Wp6Tx38IASc5qLX5j50Y1cYuTz.png", "featured_image": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/ouofNkg6EmThg8Sx8MXtHtrftjJkBiLBMjZ4pls6.png", "images": [{"key": "Unable to walk long distance/elderly", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/d8eokC2ErsVQWQU3p9i5AxdWVDFymYLvUXyBAqLb.png", "id": "1678", "key_origin": "Unable to walk long distance/elderly"}, {"key": "Unable to ascend/descend aircraft steps", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/d8eokC2ErsVQWQU3p9i5AxdWVDFymYLvUXyBAqLb.png", "id": "1678", "key_origin": "Unable to ascend/descend aircraft steps"}], "short_content": null, "exist_popup": null, "show_exit_popup": 0, "order": 0}, "Cabin Baggage": {"code": "<PERSON><PERSON><PERSON>", "title": "Upgrade Carry-on Baggage", "content": "<h2 class=\"cabin-baggage-heading\" style=\"box-sizing: border-box; margin-top: 0px; margin-bottom: 0.5rem; font-weight: 500; line-height: 1.2; font-size: 1.3rem; font-family: Dax, RSU, sans-serif;\">Upgrade to Extra Carry-On Baggage</h2>\r\n<pre><span style=\"color: #27ae60; font-family: helvetica, arial, sans-serif;\">Passengers may bring two pieces of baggage, one weighing up to 7 kg and the other,<br /> depending on the purchase, weighing no more than 3 kg.</span></pre>", "banner": "", "featured_image": "", "images": [{"key": "Extra Carry-On +3 KG", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/flqZiUqcK78OmvzDe3O4CQG83UFz9neOZyFMQ8bK.png", "id": "289", "key_origin": "Extra Carry-On +3 KG"}], "short_content": "<ul class=\"cabin-baggage-notes\">\r\n<li>Maximum weight 10kg</li>\r\n<li>Dimensions 56x36x23cm</li>\r\n</ul>", "exist_popup": "<p style=\"font-weight: 600;\"><span style=\"font-size: 12pt;\">Please check the condition of the carry-on baggage allowance below</span></p>\r\n<p style=\"margin-bottom: 10px;\"><span style=\"font-size: 12pt;\">Your carry-on baggage allowance is 2 pieces with a combined weight of 10 kilograms including One (1) Cabin baggage (maximum weight 7 kg) and One (1) Laptop bag/handbag (maximum weight 3 kg) which must be able to place and fit under the seat in front of you.</span></p>\r\n<p><span style=\"font-size: 12pt;\">The maximum of two (2) pieces must not weigh more than 10kg</span></p>", "show_exit_popup": 1, "order": 0}, "Seat": {"code": "<PERSON><PERSON>", "title": "Seat selection", "content": "<p class=\"text-red\" style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; outline: 0px; font-size: 14px; vertical-align: baseline; background: #ffffff; color: #ff0000; font-family: Dax, RSU, sans-serif;\">Free seat selection with Skyboss fare.</p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; outline: 0px; font-size: 14px; vertical-align: baseline; background: #ffffff; color: #212529; font-family: Dax, RSU, sans-serif;\">Conditions and restrictions for seats in the emergency exit area Passengers must have the following qualifications:</p>\r\n<ul style=\"box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; color: #212529; font-family: Dax, RSU, sans-serif; font-size: 14px; background-color: #ffffff;\">\r\n<li style=\"box-sizing: border-box;\">Passenger age 16 to below 65 years old</li>\r\n<li style=\"box-sizing: border-box;\">Not in any stage of pregnancy</li>\r\n<li style=\"box-sizing: border-box;\">Physically and mentally fit to assist crew in an emergency</li>\r\n<li style=\"box-sizing: border-box;\">Not traveling with infants and/or children or people who require special assistance</li>\r\n<li style=\"box-sizing: border-box;\">Not a monk and/or a novice</li>\r\n<li style=\"box-sizing: border-box;\">Capable of understanding cabin crew instruction (printed/spoken) both Thai and English</li>\r\n<li style=\"box-sizing: border-box;\">Other conditions shall be governed by the terms and conditions of an airline standard</li>\r\n<li style=\"box-sizing: border-box;\">We reserved the right to change seat where passengers conditions did not meet above qualifications</li>\r\n</ul>", "banner": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/QHIOhroiweX0zVp3xR9HY3SICt24QhWLSMoi2GEP.jpg", "featured_image": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/5KvXOhXfG4XVHqlpaR57jK1pdqQmesBXedjODVXE.png", "images": "", "short_content": null, "exist_popup": "<p style=\"font-size: 16px; font-weight: 600;\">You have selected seats in the emergency exit area. Please ensure you have the following qualifications</p>\r\n<ol style=\"font-size: 16px; margin-left: 45px;\">\r\n<li>16 years old to below 65 years old</li>\r\n<li>Not in any stage of pregnancy or suspected of becoming pregnant</li>\r\n<li>Must be healthy (physically and mentally) to assist cabin crew in case of emergency</li>\r\n<li>Not traveling with infants and / or children, or people requiring special assistance</li>\r\n<li>Not a monk or a novice</li>\r\n<li>Capable of understanding cabin crew instructions (printed/spoken) in both Thai and English</li>\r\n</ol>", "show_exit_popup": 1, "order": 1}, "Baggage": {"code": "Baggage", "title": "Baggage Option", "content": "<p><em><strong>Notes</strong></em></p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; outline: 0px; font-size: 14px; vertical-align: baseline; background: #ffffff; color: #212529; font-family: Dax, RSU, sans-serif;\">ECO</p>\r\n<p><span style=\"box-sizing: border-box; font-family: Dax, RSU, sans-serif; font-size: 14px; background-color: #ffffff; color: green;\">Your ticket class includes 7kgs carry-on baggage</span></p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; outline: 0px; font-size: 14px; vertical-align: baseline; background: #ffffff; color: #212529; font-family: Dax, RSU, sans-serif;\">DELUXE</p>\r\n<p><span style=\"box-sizing: border-box; font-family: Dax, RSU, sans-serif; font-size: 14px; background-color: #ffffff; color: green;\">Your ticket class includes 7kgs carry-on baggage, Standard Check Baggage 20kgs</span></p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; outline: 0px; font-size: 14px; vertical-align: baseline; background: #ffffff; color: #212529; font-family: Dax, RSU, sans-serif;\">SKYBOSS</p>\r\n<p><span style=\"box-sizing: border-box; font-family: Dax, RSU, sans-serif; font-size: 14px; background-color: #ffffff; color: green;\">Your ticket class includes 10kgs carry-on baggage, 1 Golf Set 15 kg, Standard Check Baggage 30kgs</span></p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; outline: 0px; font-size: 18px; vertical-align: baseline; background: #ffffff; color: #191d1b; font-family: Dax, RSU, sans-serif;\"><span style=\"box-sizing: border-box; font-weight: bolder;\"><span style=\"box-sizing: border-box; font-size: 14px; color: #000000;\">Extra Carry-On Conditions</span></span><span style=\"box-sizing: border-box; font-family: tahoma, arial, helvetica, sans-serif; font-size: 10pt; color: windowtext;\">​</span></p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; outline: 0px; font-size: 18px; vertical-align: baseline; background: #ffffff; color: #191d1b; font-family: Dax, RSU, sans-serif;\"><span style=\"box-sizing: border-box; font-family: tahoma, arial, helvetica, sans-serif;\"><span style=\"box-sizing: border-box; font-size: 13.3333px;\">1.Each passenger is allowed to carry on board two (2) pieces of cabin baggage comprising either:&nbsp; ​</span></span><span style=\"box-sizing: border-box; font-family: tahoma, arial, helvetica, sans-serif;\"><span style=\"box-sizing: border-box; font-size: 13.3333px;\">Cabin baggage 1 piece, weight must not exceed 7 KG and&nbsp; the dimensions must not exceed of 56cm (H) X 36cm (W) X 23cm (D) and MUST FIT in the overhead storage compartment in the aircraft cabin. ​</span></span><span style=\"box-sizing: border-box; font-family: tahoma, arial, helvetica, sans-serif;\"><span style=\"box-sizing: border-box; font-size: 13.3333px;\">One small handbag weight of 3 kg and fits under the seat. ​</span></span></p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; outline: 0px; font-size: 18px; vertical-align: baseline; background: #ffffff; color: #191d1b; font-family: Dax, RSU, sans-serif;\"><span style=\"box-sizing: border-box; font-family: tahoma, arial, helvetica, sans-serif;\"><span style=\"box-sizing: border-box; font-size: 13.3333px;\">2.Apply for VZ direct route only</span></span></p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; outline: 0px; font-size: 18px; vertical-align: baseline; background: #ffffff; color: #191d1b; font-family: Dax, RSU, sans-serif;\"><span style=\"box-sizing: border-box; font-family: tahoma, arial, helvetica, sans-serif;\"><span style=\"box-sizing: border-box; font-size: 13.3333px;\">3.In cases where a passenger opts to voluntarily modify their flight reservation, the Extra Carry-on Baggage that was acquired for a fee on the initial flight might not be transferrable as a result of aircraft safety regulations.</span></span></p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; outline: 0px; font-size: 18px; vertical-align: baseline; background: #ffffff; color: #191d1b; font-family: Dax, RSU, sans-serif;\"><span style=\"box-sizing: border-box; font-family: tahoma, arial, helvetica, sans-serif;\"><span style=\"box-sizing: border-box; font-size: 13.3333px;\"><span style=\"color: #191d1b; font-family: tahoma, arial, helvetica, sans-serif;\"><span style=\"font-size: 13.3333px;\">4. In the case of safety while boarding, the airlines may sometimes request passengers to check in carry-on baggage under the aircraft.</span></span></span></span></p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; outline: 0px; font-size: 18px; vertical-align: baseline; background: #ffffff; color: #191d1b; font-family: Dax, RSU, sans-serif;\">&nbsp;</p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; outline: 0px; font-size: 14px; vertical-align: baseline; background: #ffffff; color: #212529; font-family: Dax, RSU, sans-serif;\">Oversize Baggage refers item that are over the general size/weight limitation.</p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; outline: 0px; font-size: 14px; vertical-align: baseline; background: #ffffff; color: #212529; font-family: Dax, RSU, sans-serif;\">Size 3 dimensions High x Wide x Depth not exceeding to 203 cm and weight not exceed 32 kg per piece.</p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; outline: 0px; font-size: 14px; vertical-align: baseline; background: #ffffff; color: #212529; font-family: Dax, RSU, sans-serif;\">&nbsp;</p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; outline: 0px; font-size: 14px; vertical-align: baseline; background: #ffffff; color: #212529; font-family: Dax, RSU, sans-serif;\"><span style=\"box-sizing: border-box; font-weight: bolder;\">*Oversize Baggage Package selected fee is per 1 item only. However, you can combine weight with standard baggage. In case of additional oversize baggage pieces, please contact our check-in airport staff 3 hour before departure time.</span></p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; outline: 0px; font-size: 14px; vertical-align: baseline; background: #ffffff; color: #212529; font-family: Dax, RSU, sans-serif;\">&nbsp;</p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; outline: 0px; font-size: 14px; vertical-align: baseline; background: #ffffff; color: #212529; font-family: Dax, RSU, sans-serif;\"><span style=\"color: #212529; font-family: Dax, RSU, sans-serif;\"><span style=\"font-size: 14px;\"><strong>**Golf club set with The total dimension (length + width + height) of each piece does not exceed 203cm considered as Normal Checked baggage.</strong></span></span></p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; outline: 0px; font-size: 14px; vertical-align: baseline; background: #ffffff; color: #212529; font-family: Dax, RSU, sans-serif;\">&nbsp;</p>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; outline: 0px; font-size: 14px; vertical-align: baseline; background: #ffffff; color: #212529; font-family: Dax, RSU, sans-serif;\"><span style=\"box-sizing: border-box; font-weight: bolder;\">Categories of Oversize Baggage</span></p>\r\n<ul style=\"box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; color: #212529; font-family: Dax, RSU, sans-serif; font-size: 14px; background-color: #ffffff;\">\r\n<li style=\"box-sizing: border-box;\">Sports Equipment</li>\r\n<li style=\"box-sizing: border-box;\">Large Musical Instruments</li>\r\n<li style=\"box-sizing: border-box;\">Bulky Items (LCD TV, Computer, Electronic Equipment etc.)</li>\r\n</ul>\r\n<p style=\"box-sizing: border-box; margin: 0px; padding: 0px; border: 0px; outline: 0px; font-size: 14px; vertical-align: baseline; background: #ffffff; color: #212529; font-family: Dax, RSU, sans-serif;\">Acceptable Sport Equipment</p>\r\n<p><img src=\"https://d3qt33rdqglgrg.cloudfront.net/uploads/PVhQkapaxkdJVyjStaioklCXTA3cma2OahV8vmf7.png\" alt=\"\" width=\"578\" height=\"112\" /></p>", "banner": "", "featured_image": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/yd6udRRnIAWYp1kdrpZdTRQoMVPbhOkvYBR5ZWWC.png", "images": [{"key": "Bag 15kgs (VZ)", "path": null, "id": null, "key_origin": "Bag 15kgs (VZ)"}, {"key": "Bag 20kgs (VZ)", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/flqZiUqcK78OmvzDe3O4CQG83UFz9neOZyFMQ8bK.png", "id": "289", "recommend": "1", "key_origin": "Bag 20kgs (VZ)"}, {"key": "Bag 25kgs (VZ)", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/flqZiUqcK78OmvzDe3O4CQG83UFz9neOZyFMQ8bK.png", "id": "289", "key_origin": "Bag 25kgs (VZ)"}, {"key": "Bag 30kgs (VZ)", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/flqZiUqcK78OmvzDe3O4CQG83UFz9neOZyFMQ8bK.png", "id": "289", "key_origin": "Bag 30kgs (VZ)"}, {"key": "Bag 35kgs (VZ)", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/flqZiUqcK78OmvzDe3O4CQG83UFz9neOZyFMQ8bK.png", "id": "289", "key_origin": "Bag 35kgs (VZ)"}, {"key": "Bag 40kgs (VZ)", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/flqZiUqcK78OmvzDe3O4CQG83UFz9neOZyFMQ8bK.png", "id": "289", "key_origin": "Bag 40kgs (VZ)"}, {"key": "VZOversize20kg", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/LjILNe5w3hqmPZLuovcVMqEAePe3aqGkWhhWcqTg.png", "id": "307", "key_origin": "VZOversize20kg"}, {"key": "VZOversize30kg", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/LjILNe5w3hqmPZLuovcVMqEAePe3aqGkWhhWcqTg.png", "id": "307", "recommend": "1", "key_origin": "VZOversize30kg"}], "short_content": "<p><span style=\"color: #e74c3c;\">&ldquo;! Your ticket included 7 kg of carry on baggage&rdquo; for ECO, DELUXE Class​</span></p>\r\n<p><span style=\"color: #e74c3c;\">&ldquo;! Your ticket included 10 kg of carry on baggage&rdquo; for SKYBOSS Class</span></p>", "exist_popup": null, "show_exit_popup": 0, "order": 2}, "Meal": {"code": "<PERSON><PERSON>", "title": "Sky Café Meal", "content": null, "banner": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/aC3evVPIaNdE0Ykn6mCNlrkFsngeR35oG9YODk6z.jpg", "featured_image": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/pXW4IIexk87mabsAwut8D3S4lMuIakWRv9HJjOUd.png", "images": [{"key": "Combo chicken Te<PERSON>ki with rice and water", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/u7WuWAGv5VfCMt4aHCtW8iHmZsrtkY3UOok1naOY.jpg", "id": "1715", "key_origin": "Combo chicken Te<PERSON>ki with rice and water"}, {"key": "Combo chicken green curry with rice and water", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/yzBTKXVqoLyhVve9oDNoOodVCyQJIh6TVC1SECyd.jpg", "id": "1714", "key_origin": "Combo chicken green curry with rice and water"}, {"key": "Combo Spicy Chicken Basil with rice and water", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/6mwWCq1ZM3yoKMZVvJHUUnH7RHmdsZmnpgJ4rg7s.jpg", "id": "1713", "key_origin": "Combo Spicy Chicken Basil with rice and water"}, {"key": "Vegetarian rice and water", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/bPSFCWnOhSbpJwuYfq5mwVNBj4wqGrgghtKQGXIH.jpg", "id": "1716", "key_origin": "Vegetarian rice and water"}, {"key": "Combo roasted chicken noodle and water", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/CgOrxIYz2nLt5w3gwn4HH0V5pZ1by1etG2yfXFDL.jpg", "id": "1717", "key_origin": "Combo roasted chicken noodle and water"}, {"key": "Combo Shrimp Pad Thai and water", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/Pd3FBaihYh8DefPx26UxhzUeEnjPvElFRG2zCewN.jpg", "id": "1718", "key_origin": "Combo Shrimp Pad Thai and water"}, {"key": "Combo Chicken Lasagna and water", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/KGQGzoyHedgyDiubwosYcFaOHILZyGi8MJ9OM63P.jpg", "id": "1719", "key_origin": "Combo Chicken Lasagna and water"}, {"key": "Combo Ham Cheese Sandwich with water or hot drink", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/wmgFGD0PzNTL2JjiXkg5VDM5qxtK9w11ceeoE1PY.jpg", "id": "649", "key_origin": "Combo Ham Cheese Sandwich with water or hot drink"}, {"key": "Combo Ham Cheese Sandwich with water or hot drink", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/tWVOy1vYiMtaHQmR6DcWmNK0mYGXwAUQk2higvbn.jpg", "id": "648", "key_origin": "Combo Ham Cheese Sandwich with water or hot drink"}, {"key": "Combo Tuna Croissant with water or hot drink", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/CEGVZc58hOlfLYsGPkGV6pZaEFeMkYWVa2awhk5b.jpg", "id": "645", "key_origin": "Combo Tuna Croissant with water or hot drink"}, {"key": "Combo Tuna Croissant with water or hot drink", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/6DJDxI3vBBkHI6Rt7MhZDWepkSBuuVZvhZJQm52N.jpg", "id": "646", "key_origin": "Combo Tuna Croissant with water or hot drink"}, {"key": "Combo Chicken Ham Egg sandwich and water or hot drink", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/nHQJFG0BbtMxQ4PB05n2jPQXrWNmiK8QDo7hlq3J.jpg", "id": "655", "key_origin": "Combo Chicken Ham Egg sandwich and water or hot drink"}, {"key": "Combo Chicken Ham Egg sandwich and water or hot drink", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/FakfKM0ul5R7Lx7G1AgKj19Z61lyTkzGKSLPcLO2.jpg", "id": "656", "key_origin": "Combo Chicken Ham Egg sandwich and water or hot drink"}, {"key": "Combo Spinach Danish and water or hot drink", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/N9F4NvLFZh9gtWzRQ3kI1ITbBaGUKhQYzxWN4Hg1.jpg", "id": "653", "key_origin": "Combo Spinach Danish and water or hot drink"}, {"key": "Combo Spinach Danish and water or hot drink", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/WgsgNQYSsa3XOhkeYbWGBh7pXuhoa3jRxcYO69ZX.jpg", "id": "654", "key_origin": "Combo Spinach Danish and water or hot drink"}, {"key": "Combo Butter Bun and Mixed fruit tea", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/nxK2Z3HMxPncZaIVdSwYARAvu2t7UhI1coPUYBVt.png", "id": "658", "recommend": "1", "key_origin": "Combo Butter Bun and Mixed fruit tea"}, {"key": "Combo Milk Bun and Mixed fruit tea", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/1dw5PLimZM7s6hZ6qGIjFEN1GJQnPoCBJZmzLsmD.png", "id": "659", "recommend": "1", "key_origin": "Combo Milk Bun and Mixed fruit tea"}, {"key": "<PERSON><PERSON>", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/bcF4SxDWtbkjg5mxAmL7CeP6Jtk4zfgmmFOT2BJX.jpg", "id": "680", "recommend": "1", "key_origin": "<PERSON><PERSON>"}, {"key": "Blueberry Muffin", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/PV9JDh4UdDjyZCxj1DCznrgZzfFWW5WuELjFLk4V.jpg", "id": "678", "recommend": "1", "key_origin": "Blueberry Muffin"}, {"key": "Chocolate Chip Muffin", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/CNvgMTn0B77X3UzaWjYwRHnsliCnZwJl2PEnlNP7.jpg", "id": "679", "key_origin": "Chocolate Chip Muffin"}, {"key": "Mango Sticky rice", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/IXQBv7qy9iuHe1BPtgBbA7FFsImf0n9L6qCLIH4w.png", "id": "692", "recommend": "1", "key_origin": "Mango Sticky rice"}, {"key": "Milk tea with brown sugar", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/qH0xuZzZje8AFuj74jLwSBYfJ2F4ObETRy0SB6A7.jpg", "id": "672", "key_origin": "Milk tea with brown sugar"}, {"key": "Thai tea with brown sugar", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/4jQJZqBmY9yeN0JKysPZ1OFCy0YaRQtRvPJRGsmt.jpg", "id": "674", "key_origin": "Thai tea with brown sugar"}, {"key": "Matcha green tea with brown sugar", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/e076ch9RW27sHe8Mwt5nHsPw66JXNhDAr6H91iX1.jpg", "id": "676", "key_origin": "Matcha green tea with brown sugar"}, {"key": "Iced Cocoa", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/hIaL2f2li5ywOkMwBsVUXk5ecF7soX6UtNDLzm6L.png", "id": "693", "key_origin": "Iced Cocoa"}, {"key": "Iced Latte", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/80vLmrOTwgFZNnSekI7Fz8LQ4vHzsdMQ2Dnx8Fbl.jpg", "id": "675", "key_origin": "Iced Latte"}, {"key": "Iced <PERSON>o", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/oRqizHoH6cs8NGdo0EutvNaVX6MPdCVaOtXV7XYr.jpg", "id": "673", "key_origin": "Iced <PERSON>o"}, {"key": "Iced Orange Americano", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/JbpVe061Z2x4g9LHaUzMtMDA3Yx7X7y6pHMhHiW7.jpg", "id": "677", "key_origin": "Iced Orange Americano"}, {"key": "Mixed fruit tea", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/k4aNSDTrQKdcGWNjFbt8Uv1jFvlHEBmKgIJ1oSAc.png", "id": "644", "key_origin": "Mixed fruit tea"}, {"key": "Combo Spaghetti and Water and Cashew", "path": null, "id": null, "key_origin": "Combo Spaghetti and Water and Cashew"}, {"key": "Combo Thai Fried rice and Water and Cashew", "path": null, "id": null, "key_origin": "Combo Thai Fried rice and Water and Cashew"}, {"key": "Combo Crab and Shrimp Glass Noodles and Water and Cashew", "path": null, "id": null, "key_origin": "Combo Crab and Shrimp Glass Noodles and Water and Cashew"}, {"key": "Combo Singapore Noodles and Water and Cashew", "path": null, "id": null, "key_origin": "Combo Singapore Noodles and Water and Cashew"}, {"key": "Combo Vegetarian Yang Chow Fried Rice and Water and Cashew", "path": null, "id": null, "key_origin": "Combo Vegetarian Yang Chow Fried Rice and Water and Cashew"}, {"key": "Combo Bread and Water and Cashew", "path": null, "id": null, "key_origin": "Combo Bread and Water and Cashew"}, {"key": "Souffle Cheesecake", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/czwngobyi5RgRtC44BisVKHKNauMW2yVRaBuGjM1.png", "id": "1658", "key_origin": "Souffle Cheesecake"}, {"key": "Timber Ring", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/X8iAFPO0psq8UWzccAwgxnbccndMzCCmjfQh2pnq.jpg", "id": "2061", "key_origin": "Timber Ring"}, {"key": "<PERSON>", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/pFXYKw4Hn3j7her2Bz3aRdeyFC8jzWEdV6ULOTYm.jpg", "id": "1588", "key_origin": "<PERSON>"}, {"key": "Combo Spicy Chicken Basil and Sprite", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/H3ExnNkwWQfzKM8CLh7WUmVGuagKRwsMbFybjpvS.png", "id": "1630", "key_origin": "Combo Spicy Chicken Basil and Sprite"}, {"key": "Combo Chicken Teriyaki and Sprite", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/P1Bm9xyza7QR14isbNLSIWEB5UzGJLN6OMWojWzS.png", "id": "1629", "key_origin": "Combo Chicken Teriyaki and Sprite"}, {"key": "Combo Roasted chicken noodle and Sprite", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/D63L3012SZnWcFgmkaA8MGYMs4wnklKamf6UrUFx.png", "id": "1640", "key_origin": "Combo Roasted chicken noodle and Sprite"}, {"key": "Combo Chicken green curry and Sprite", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/ZGCW55bJVUETKVlsuHbEvL0Q3WUK0O1kIeQrFt1j.png", "id": "1638", "key_origin": "Combo Chicken green curry and Sprite"}, {"key": "Combo Shrimp Pad Thai and Sprite", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/IKwA6b5VaYvDZ6JBC7WMfU5VirBsAEnv9Z7idmm7.png", "id": "1712", "key_origin": "Combo Shrimp Pad Thai and Sprite"}, {"key": "Combo Lasagna and Sprite", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/8V7csrMOq6Sh6U7NRzVa1i2ExffZGWbgMBAppXbg.png", "id": "1723", "key_origin": "Combo Lasagna and Sprite"}, {"key": "Combo Vegetarian Fried rice and Sprite", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/m1cws62gsJRLG4qPKgUZtK7YXDTTv3GsGz7Jtasn.png", "id": "1627", "key_origin": "Combo Vegetarian Fried rice and Sprite"}, {"key": "Chicken Burger and water or hot drink", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/gSKj5opdkOYAzD22gRcTOzXxtYW0FRmqR7OtKYGV.png", "id": "1643", "key_origin": "Chicken Burger and water or hot drink"}, {"key": "Banana Cake", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/x8wYnz86RbdTDpDefeWru22F5cAYZYfhG2JVsNOK.png", "id": "1659", "key_origin": "Banana Cake"}, {"key": "Strawberry Bun and Mixed fruit tea", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/OySGZXqrzZZLTKFL9gJcKsYDcZTYN5eUQn1x6SjW.png", "id": "1660", "key_origin": "Strawberry Bun and Mixed fruit tea"}, {"key": "Crispy Brownie cube and Hot drink", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/cmxqqXJ2YN4XGs4ZRspmKGfV7bqYYseWkRxz7fiJ.png", "id": "1661", "key_origin": "Crispy Brownie cube and Hot drink"}, {"key": "Cranberry crispy chocolate and Hot drink", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/yvcmetJXciUHSzXHdsE2rhFvdhuftRtdxdVWT8Sv.png", "id": "1662", "key_origin": "Cranberry crispy chocolate and Hot drink"}, {"key": "Milk Bun, Strawberry Bun, Souffle cheesecake and Banana cake", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/7AfyWshqpAJgdb52nJ9h1Hp1velknOj6NDSOwWVy.png", "id": "1663", "key_origin": "Milk Bun, Strawberry Bun, Souffle cheesecake and Banana cake"}, {"key": "Souffle Cheesecake and Mixed fruit tea", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/ZERkflKCXWHkvyhgPK5189UAZdUEJhfaSDrKbpgF.jpg", "id": "1673", "key_origin": "Souffle Cheesecake and Mixed fruit tea"}, {"key": "Banana Cake and Mixed fruit tea", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/jWYpXCyiLVsbAbZ02qBj9Ht1XaImUYsGdzMVSShx.jpg", "id": "1674", "key_origin": "Banana Cake and Mixed fruit tea"}, {"key": "Stir fried minced chicken with soy bean paste", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/TaQ2xQ6qaW18sxW2u3MNio1Omw21bFajbXAGaQc5.png", "id": "1793", "key_origin": "Stir fried minced chicken with soy bean paste"}, {"key": "Fried Hainanese chicken rice mix", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/cZqIE9Y0i6bCVaPoVSl3jz0ZUsA4d5o5TubcBdY2.png", "id": "1790", "key_origin": "Fried Hainanese chicken rice mix"}, {"key": "Temple noodles with seafood", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/mJDDfG8Uj2eZUQNbhlQorjZPnwfQbDzKDPAJtATW.png", "id": "1792", "key_origin": "Temple noodles with seafood"}, {"key": "Wine marinated catfish with basil", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/cMNf6I9J7Qw27bt41r0GpiYEk54RjLIqCgQ5ARfy.png", "id": "1794", "key_origin": "Wine marinated catfish with basil"}, {"key": "Braised chicken with cream curry sauce", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/jFiVAjC470zw3J639CK6BpeKf4JH82LMq78itafR.png", "id": "1791", "key_origin": "Braised chicken with cream curry sauce"}, {"key": "Stir fried bamboo shoot", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/ivZKc67ss1aiyuPA90Ak36UuJIhCmXAbgUqfhYhb.png", "id": "1789", "key_origin": "Stir fried bamboo shoot"}, {"key": "French Cheese Bread with Chicken Ham Egg Salad", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/5H7GgjopEDWg8QwHlK1WSYa4Q3yl7ttTwEHXFTyV.jpg", "id": "1795", "key_origin": "French Cheese Bread with Chicken Ham Egg Salad"}, {"key": "Combo Stir-fried minced chicken with soy bean paste and water", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/phNPY6ZPRxJxTD49Au40ruXalH7pclPNeS5tqUHl.jpg", "id": "1828", "key_origin": "Combo Stir-fried minced chicken with soy bean paste and water"}, {"key": "Combo Fried Hainanese chicken rice mix and water", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/73VBsjFbpbIVnl6UieaZXUaamckJ610FzWzG7sFi.jpg", "id": "1831", "key_origin": "Combo Fried Hainanese chicken rice mix and water"}, {"key": "Combo Temple noodles with seafood and water", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/vkW4eAM0vlb5wiWu4vlm0X7xIGJ8Vu00tX8PUHu1.jpg", "id": "1832", "key_origin": "Combo Temple noodles with seafood and water"}, {"key": "Combo Wine-marinated catfish with basil and water", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/xR8pka8WSgFopsduREdepGQZU4o0cBe72OZDVwcj.jpg", "id": "1830", "key_origin": "Combo Wine-marinated catfish with basil and water"}, {"key": "Combo Braised chicken with cream curry sauce and water", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/wpthy76EpIJPwJC5ZQmQSjwcqCX1YkIQFUrCDQPG.jpg", "id": "1833", "key_origin": "Combo Braised chicken with cream curry sauce and water"}, {"key": "Combo Chicken Ham Egg sandwich and Iced Americano", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/AfeCzKdkNRmFtlDcW3zggSQAITQsQ96JH85skjd8.png", "id": "1887", "key_origin": "Combo Chicken Ham Egg sandwich and Iced Americano"}, {"key": "Combo Chicken Ham Egg sandwich and Iced Orange Americano", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/uSdflbwYBE8vFoHtQMBOYCASHyWUlOmSTd6X6hcz.png", "id": "1888", "key_origin": "Combo Chicken Ham Egg sandwich and Iced Orange Americano"}, {"key": "Combo Chicken Ham Egg sandwich and Matcha green tea with brown sugar", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/X0jxIpHjiFfv4z5IXNFE0a8h3dE8IS3sIfAzgcPw.png", "id": "1889", "key_origin": "Combo Chicken Ham Egg sandwich and Matcha green tea with brown sugar"}, {"key": "Combo Crab Strick Croissant and water or hot drink", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/5x69zAWK7F3yZnnnqCMUbemSUBXskPysfN7r39m7.jpg", "id": "2050", "key_origin": "Combo Crab Strick Croissant and water or hot drink"}, {"key": "Combo Truffle Mushroom Roll and water or hot drink", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/acClBW5df4osAkeNhs5epqSK9gsCB65LUf5GCb32.jpg", "id": "2051", "key_origin": "Combo Truffle Mushroom Roll and water or hot drink"}, {"key": "Combo Stir Fried Flat Noodles with Black Soy Sauce and Chicken and water", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/zwTGErwhf92VO2HKg9l8alAFUOGR1abSDRD8ju7S.png", "id": "2052", "key_origin": "Combo Stir Fried Flat Noodles with Black Soy Sauce and Chicken and water"}, {"key": "Combo Nutella bun and mixed fruit tea", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/mfTYgNcGb3nQ5vbQSVySS9G2zn9U54E4sEtgUJ3m.jpg", "id": "2053", "key_origin": "Combo Nutella bun and mixed fruit tea"}, {"key": "Thai tea brown sugar with whipping cream", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/ZLyDsFn8mWiN21cKTyK5BbMq0gstYQzuYplpfwVO.jpg", "id": "2057", "key_origin": "Thai tea brown sugar with whipping cream"}, {"key": "Milk tea brown sugar with whipping cream", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/Rx2sP7QONa83b97ANW1193XA1EqFE4rp6ywJc3Cq.jpg", "id": "2060", "key_origin": "Milk tea brown sugar with whipping cream"}, {"key": "Cocoa with whipping cream", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/ij7C1hjKTypNsjQxSfOTbcdEWlGredJRR5US8W0h.jpg", "id": "2058", "key_origin": "Cocoa with whipping cream"}, {"key": "Carrot cake", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/YdFLSFKbXO3vuxuuMAQxV4Zj1wFKiDvsF2qYx6dR.jpg", "id": "2059", "key_origin": "Carrot cake"}, {"key": "Combo White Sauce Penne and water", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/7YpPD7NlBARWMcLaMjMcToEO2J7bWcVnfH2GOfm2.jpg", "id": "2062", "key_origin": "Combo White Sauce Penne and water"}, {"key": "Combo Spicy Chicken Basil with rice and water and macadamia 30g", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/DgipSLuoR6BvvXSkFPlr1xVofNEJ5xQoEuN7UYGs.jpg", "id": "2083", "key_origin": "Combo Spicy Chicken Basil with rice and water and macadamia 30g"}, {"key": "Combo Chicken Biryani and water", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/FPnVWs5sJLwLDj9ablDoGqAowWp0HgYjvSPQB3Wp.jpg", "id": "2084", "key_origin": "Combo Chicken Biryani and water"}, {"key": "Combo Vegetarian Biryani and water", "path": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/YSXE1KkaK2I7JsmTacxTRaNagCHKjoujRotqSSkO.jpg", "id": "2085", "key_origin": "Combo Vegetarian Biryani and water"}], "short_content": null, "exist_popup": null, "show_exit_popup": 0, "order": 3}, "Priority": {"code": "Priority", "title": "Priority Check-in", "content": null, "banner": "", "featured_image": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/sn0BTklmGA6mUvAKARPEflE6n3yEny9dYspFcNFg.png", "images": "", "short_content": null, "exist_popup": null, "show_exit_popup": 0, "order": 4}, "Vip Lounge": {"code": "Vip Lounge", "title": "Vip Lounge", "content": "<p><strong>Operation Time:</strong></p>\r\n<p>Domestic : 05.30 AM - 10.00 PM</p>\r\n<p>International : 24 Hours.</p>\r\n<p><strong>Term &amp; Conditions:​</strong></p>\r\n<ol>\r\n<li>​ Service is Available for a maximum for 2.5 hours per person per service time.</li>\r\n<li>&nbsp;Above 30 minutes of use service, Lounge may charge passengers automatically applied to 1-hour charge.</li>\r\n<li>&nbsp;24 hours advance booking is required to book Lounge service.</li>\r\n<li>&nbsp;Lounge Service option is non-refundable* and non-cancellation*</li>\r\n<li>&nbsp;Lounge service is valid on your travel date. Passengers cannot use before or backward travel dates.</li>\r\n<li>&nbsp;Lounge service available for some destinations.</li>\r\n<li>&nbsp;Lounge Service availability of food and/or beverages at lounge may vary at airports based on package</li>\r\n<li>&nbsp;Lounge Service Price included VAT 7%</li>\r\n</ol>\r\n<p>- Lounge Domestic/Inter outbound <span style=\"color: #e74c3c;\"><a style=\"color: #e74c3c; text-decoration: underline;\" href=\"https://bit.ly/VZLD_prebook\" target=\"_blank\" rel=\"noopener\">Read More!</a></span> Your Lounge Benefits.</p>\r\n<p>- Lounge Inter Inbound <span style=\"color: #e74c3c;\"><a style=\"color: #e74c3c;\" href=\"https://bit.ly/VZLO_Lounge\">Read More!</a></span> Your Lounge Benefits.</p>\r\n<p><span style=\"color: #e74c3c;\"><strong>&gt;&gt; Please present your Travel Itinerary and Boarding Pass to Entry Lounge &lt;&lt;</strong></span></p>\r\n<p><strong>*Remark</strong>: Depends on Terms &amp; Condition and policy of irregularities</p>", "banner": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/vFdMd7Rr6cMUY5E6WlKrwZm6klqHy8DMf02NHkdc.jpg", "featured_image": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/IJ4s77hUZF2WkGDUOe3CfOSQodR6tSUjwQY8aimI.png", "images": "", "short_content": null, "exist_popup": null, "show_exit_popup": 0, "order": 5}, "Insurance": {"code": "Insurance", "title": "Travel Insurance", "content": null, "banner": "", "featured_image": "https://s3.ap-southeast-1.amazonaws.com/th.vietjetair.com/uploads/doUH0Z9RZmvz9TmpZIhuttuydQevCIG8R1Xn9W4R.png", "images": "", "short_content": "<p><strong>Protected by Travel Insurance</strong></p>\r\n<p>&radic; Cover up to 2,500,000 baht*​</p>\r\n<p>&radic; Covering medical expenses abroad​</p>\r\n<p>&radic; Baggage protection against loss and damage​</p>\r\n<p>&radic; Flight delay coverage​</p>\r\n<p>*According to the selected insurance plan and conditions specified by the company​</p>\r\n<p>Study the details on coverage, exclusions, and other terms &amp; conditions.</p>\r\n<ul>\r\n<li>For Domestic Route: <a href=\"https://www.msig-thai.com/thaivietjetair/MSIG-Travel-Insurance-EN.html\" target=\"_blank\" rel=\"noopener\">Read More!</a></li>\r\n<li>For International Route: <a href=\"https://www.msig-thai.com/thaivietjetair/MSIG-Travel-Insurance-inter-EN.html\" target=\"_blank\" rel=\"noopener\">Read More!</a></li>\r\n</ul>", "exist_popup": null, "show_exit_popup": 0, "order": 6}}, "taxDepartAmount": 303.6, "taxReturnAmount": 0, "isVZ": true, "insurance_config": {"unit_price": "99", "title": "Travel Insurance", "passportRequired": false, "content": {"id": 28, "title": "MSIG INSURANCE POLICIES ONE WAY", "slug": "msig-insurance-policies-one-way", "content": "<p><strong>SKY INSURANCE POLICIES ONE WAY</strong></p><p>&nbsp;</p><figure class=\"table\"><table><tbody><tr><td><strong>Coverages</strong></td><td><strong>Sum Insured (THB)</strong></td></tr><tr><td>1.&nbsp; Loss of Life, Dismemberment, Loss of Sight or Total Permanent Disability due to Accident&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td>THB 1,000,000</td></tr><tr><td><p>2. Trip Cancellation or Postponement by Passenger due to Specified Causes*</p><p>2.1 Airfares purchased through online booking services of the Policy Holder.<br>2.2&nbsp; Other Expenses<br>Total benefit for&nbsp;&nbsp; No. 2.1 and 2.2 combined</p></td><td><p>&nbsp;</p><p>2.1 &nbsp;Actual Expense up to THB 10,000</p><p>2.2 Actual Expense up to THB 10,000</p><p>In total&nbsp; not exceeding THB 10,000</p></td></tr><tr><td><p>3.&nbsp; Flight Delay due to ;</p><p>&nbsp;&nbsp;&nbsp;&nbsp; 1) Bad weather conditions;</p><p>&nbsp;&nbsp;&nbsp;&nbsp; 2) Mechanical problems of the aircraft;</p><p>&nbsp;&nbsp;&nbsp;&nbsp; 3) Strike or protest by employees of commercial airline or airport preventing the departure</p></td><td>THB 2,000 for every 5 consecutive hours of delay up to THB 10,000 maximum</td></tr><tr><td>4. Baggage Delay</td><td>THB 2,000 for every 5 consecutive hours for delay up to THB 10,000 maximum</td></tr><tr><td>5.&nbsp; Loss of or Damage to Baggage and/or Personal Effects and/or Golf Equipment maximum payable per actual expense&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td><p>THB 2,000 per item</p><p>Maximum THB 10,000 in aggregate</p><p>(Deductible 1,000 Baht)</p></td></tr><tr><td>6.&nbsp; Personal Liability (anyone occurrence and in aggregate for policy period)</td><td>Up to THB 200,000</td></tr><tr><td>7. Medical Expense due to Coronavirus 2019 (COVID-19) Infection while travelling in Flight only</td><td>Actual Expense up to THB 10,000</td></tr></tbody></table></figure><p>&nbsp;</p><figure class=\"table\"><table><tbody><tr><td><strong>Acceptance Conditions :</strong></td></tr><tr><td><p>•Insurance Plan is applicable for Insured aged more than 2-80 years old residing in Thailand only.</p><p>•&nbsp;&nbsp; * Infant age 9 days to 2 years old travelling with their parents will be covered for coverage 1 ;Loss of Life, Dismemberment, Loss of Sight or Total Permanent Disability due to Accident; for free of premium charged with 10% of the above table limit.</p></td></tr><tr><td>•This Plan covers trip within Thailand only.</td></tr><tr><td><p>•Applicant must be in good health conditions and with no disability.</p><p>•Sky Travel Care is a non-cancellation plan.</p><p>•Sky Travel Care is insured by MSIG Insurance (Thailand) PCL.</p><p>•* Trip Cancellation or Postponement - Insured is cancelled or postponed due to:</p><p>&nbsp;&nbsp;&nbsp; 1. Death or Serious Injury or Sickness of the Insured.</p><p>&nbsp;&nbsp;&nbsp; 2. Death or Serious Injury or Sickness of the Insured’s Family Members.</p><p>&nbsp;&nbsp;&nbsp; 3. Damage to the residence of the Insured caused by fire, lightning, explosion caused by cooking gas, including natural perils.</p><p>•Please <a href=\"https://www.msig-thai.com/thaivietjetair/MSIG-Travel-Insurance-EN.html\"><strong>click here to</strong></a><strong> </strong>study the details on coverage, exclusions, and other terms &amp; conditions.</p></td></tr></tbody></table></figure><p>&nbsp;</p><p>Disclaimer :</p><p>•The information contained in this webpage is intended as general information and is not an insurance policy or contract of insurance.</p><p>•All insurance-related matters after purchase are solely between the Insured Person and MSIG.</p><p><strong>For General Inquiries</strong><br><strong>Tel: +66 (0) 2 491 2929</strong><br><strong>(Mon-Fri 08:30-21:00 / Sat 09:00-18:30) except Sat and public holiday</strong><br><strong>Email: </strong><a href=\"mailto:<EMAIL>\"><strong><EMAIL></strong></a></p><p>&nbsp;</p><p><strong>For Claim Inquiries</strong><br><strong>MSIG Claims Hotline&nbsp;</strong><br><strong>Tel: 1259 (press 2)</strong></p><p><strong>Email: </strong><a href=\"mailto:<EMAIL>\"><strong><EMAIL></strong></a><strong>&nbsp;</strong></p><p>&nbsp;</p>", "channel": null, "locale": "en", "published_at": "2021-08-23T09:08:44.364Z", "created_at": "2021-08-23T09:08:00.650Z", "updated_at": "2021-10-12T04:08:27.592Z", "description": null, "banner": null, "cover": null, "channels": [{"id": 2, "name": "Fun member", "published_at": "2021-08-23T09:01:46.419Z", "created_at": "2021-08-23T09:01:42.933Z", "updated_at": "2022-08-18T03:47:38.934Z", "slug": "skyfun", "category": null, "Image": []}], "localizations": [{"id": 29, "locale": "th", "published_at": "2021-08-23T09:09:34.978Z"}]}, "checked": true}}, "status": 1}