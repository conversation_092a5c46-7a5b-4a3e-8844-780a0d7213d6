import time
import curl_cffi
from loguru import logger
import or<PERSON><PERSON>
import pytest

from app.clients.vz_client import VZClient
from app.services import vz_hood_service


def test_search():
    client = VZClient()

    trip_type = 'onewaytrip'
    from_where = 'BKK'
    to_where = 'CEI'
    start = '25/04/2025'
    end = '25/04/2025'
    adult_count = 1
    child_count = 0
    infant_count = 0
    promo_code = ''
    currency = 'idr'
    # client.host='*************'
    start_time = time.time()
    result = client.search(
        trip_type=trip_type,
        from_where=from_where,
        to_where=to_where,
        start=start,
        end=end,
        adult_count=adult_count,
        child_count=child_count,
        infant_count=infant_count,
        promo_code=promo_code,
        currency=currency.upper(),
        timeout=30,
        max_retries=3,
        pre_call=lambda: logger.warning('set proxy'),
        exceptions=(
            curl_cffi.requests.exceptions.Timeout,
            curl_cffi.requests.exceptions.HTTPError,
            curl_cffi.requests.exceptions.ConnectionError,
        ),
    )
    logger.debug(result)
    end_time = time.time()
    logger.debug(f'time: {end_time - start_time}')
    assert result is not None


def test_add_to_cart():
    client = VZClient()
    search_r = None
    with open('tests/vz_search_result.json', 'r') as f:
        search_r = orjson.loads(f.read())

    hood_flight = vz_hood_service.find_hood_flight(search_r=search_r, exclude_flights='VZ200', tag_cabin_level='Eco')
    logger.debug(hood_flight)

    api_uuid = search_r['data']['api_uuid']

    result = client.add_to_cart(
        api_uuid=api_uuid, trip_type='onewaytrip', expired_booking=30, departure=hood_flight, seat_code='Eco'
    )
    assert result is not None
