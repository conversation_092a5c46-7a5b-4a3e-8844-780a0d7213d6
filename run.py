import asyncio
from app.config import settings

import typer

from app.services import script_service

cli = typer.Typer()


@cli.command()
def debug():
    """调试"""
    # asyncio.run()
    pass


@cli.command()
def run_check(interval: int = 120, concurrency: int = 1):
    loop = asyncio.get_event_loop()
    try:
        loop.run_until_complete(script_service.run_check(interval=interval, concurrency=concurrency))
    finally:
        loop.close()


if __name__ == '__main__':
    cli()
